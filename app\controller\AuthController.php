<?php

class AuthController extends BaseController
{

    private $model;
    private $loginModel;
    private $registerModel;
    private $authService;



    public function __construct($request, $response)
    {
        parent::__construct($request, $response);
        $this->model = new CrudModel('users');
        $this->loginModel = new LoginModel();
        $this->registerModel = new RegisterModel();


    }

    public function login()
    {
        try {

            $input = json_decode(file_get_contents('php://input'), true);

            $username = $input['identifier'] ?? null;
            $password = $input['password'] ?? null;
    
            if (empty($username) || empty($password)) {
                http_response_code(400);
                echo json_encode(['error' => 'Username and password are required']);
                return;
            }

            
            $formData = ['email' => $username, 'password' => $password];
            $validationErrors = $this->loginModel->validateUser($formData);

            if ($validationErrors['success'] == false) {
                http_response_code(401);
                echo json_encode(['error' => 'Invalid username or password']);
                return;
            } elseif ($validationErrors['success'] == true) {
                $user = $this->loginModel->getUserByUsername($formData['email']);
                $token = $this->generateJwt($user['id'], $user['role']);
                http_response_code(200);

    
                echo json_encode([
                    'jwt' => $token,
                    'user' => [
                        'id' => $user['id'],
                        'name' => $user['name'],
                        'last_name' => $user['last_name'],

                        'username' => $user['username'],
                        'email' => $user['email'],

                        'phone' => $user['phone'],
                        'state' => $user['state'],
                        'city' => $user['city'],
                        'address' => $user['address'],
                        'created_at' => $user['created_at'],
                    ]
                ]);
    
            }

        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode($e->getMessage());
        }
    }

}
