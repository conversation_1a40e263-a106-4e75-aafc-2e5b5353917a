<?php

class AuthService extends BaseController
{
    private $users;


    public function __construct()
    {
        $this->users = new CrudModel('users');

    }
    public function admin()
    {

        if ($this->isLoggedIn()) {
            if ($_SESSION['role'] !== 'admin') {
                return false;
            }
            else
            {
                return true;
            }
        }else{
            return false;
        }

    }

    public function owner()
    {

        if ($this->isLoggedIn()) {
            if ($_SESSION['role'] !== 'store_owner') {
                return false;
            }
            else
            {
                return true;
            }
        }else{
            return false;
        }

    }

    public function employee()
    {

        if ($this->isLoggedIn()) {
            if ($_SESSION['role'] !== 'employee') {
                return false;
            }
            else
            {
                return true;
            }
        }else{
            return false;
        }

    }

    public function delivery()
    {

        if ($this->isLoggedIn()) {
            if ($_SESSION['role'] !== 'delivery_boy') {
                return false;
            }
            else
            {
                return true;
            }
        }else{
            return false;
        }

    }



    protected function isLoggedIn()
    {
        return isset($_SESSION['user_id']);
    }

    protected function requireLogin()
    {
        if (!$this->isLoggedIn()) {
            $this->redirect('/login');
        }
    }

    protected function sendOtpToUser($number)
    {
        $token = '6f3e94d2bca14fd89a378d2991c448b5';
        $url = 'https://otp.ubarum.com/otp.php';
        $source = 'wardena';

        $postData = http_build_query([
            'number' => $number,
            'source' => $source,
            'token' => $token,
        ]);

        $options = [
            'http' => [
                'header'  => "Content-type: application/x-www-form-urlencoded\r\n",
                'method'  => 'POST',
                'content' => $postData,
            ],
        ];

        $context = stream_context_create($options);
        $result = file_get_contents($url, false, $context);
        $this->logError($result);


        return json_decode($result, true);
    }
    


}
