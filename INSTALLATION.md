# دليل تثبيت وتشغيل Wardena API

## نظرة عامة
Wardena API هو نظام إدارة متاجر ومنتجات وطلبات مع نظام توصيل متكامل.

## متطلبات النظام

### الحد الأدنى:
- **PHP**: 7.4+ (يُفضل 8.0+)
- **MySQL**: 5.7+ (يُفضل 8.0+)
- **Apache/Nginx**: مع mod_rewrite
- **مساحة**: 500MB+
- **ذاكرة**: 128MB+

### إضافات PHP المطلوبة:
```
PDO, PDO_MySQL, JSON, OpenSSL, cURL, GD, Fileinfo, Mbstring
```

## التثبيت المحلي (Local Development)

### 1. تحضير البيئة
```bash
# استنساخ المشروع
git clone https://github.com/your-repo/wardena-api.git
cd wardena-api

# إنشاء ملف البيئة
cp .env.example .env
```

### 2. إعد<PERSON> قاعدة البيانات
```bash
# إنشاء قاعدة بيانات
mysql -u root -p
CREATE DATABASE wardena_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER 'wardena'@'localhost' IDENTIFIED BY 'your_password';
GRANT ALL PRIVILEGES ON wardena_db.* TO 'wardena'@'localhost';
FLUSH PRIVILEGES;
EXIT;
```

### 3. تحديث ملف .env
```env
APP_ENV=development
DB_HOST=localhost
DB_NAME=wardena_db
DB_USER=wardena
DB_PASS=your_password
URL=http://localhost/wardena-api/
```

### 4. تشغيل إعداد قاعدة البيانات
```bash
# من سطر الأوامر
php config/database-setup.php

# أو من المتصفح
http://localhost/wardena-api/config/database-setup.php
```

### 5. إعداد الصلاحيات
```bash
chmod 755 public/uploads/
chmod 755 storage/
chmod 644 .env
```

## التثبيت على الاستضافة الخارجية

### 1. رفع الملفات
- ارفع جميع الملفات إلى مجلد الجذر
- تأكد أن `public` هو المجلد الرئيسي للموقع

### 2. إعداد قاعدة البيانات
```sql
-- من phpMyAdmin أو لوحة التحكم
-- استورد ملف config/general.sql
-- أو قم بتشغيل:
SOURCE config/general.sql;
```

### 3. تحديث .env للإنتاج
```env
APP_ENV=production
DB_HOST=your-db-host
DB_NAME=your-db-name
DB_USER=your-db-user
DB_PASS=your-secure-password
URL=https://yourdomain.com/
```

### 4. تفعيل HTTPS
```apache
# في .htaccess
RewriteCond %{HTTPS} off
RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]
```

## إعدادات الخادم

### Apache Virtual Host
```apache
<VirtualHost *:80>
    ServerName wardena.local
    DocumentRoot /path/to/wardena-api/public
    
    <Directory /path/to/wardena-api/public>
        AllowOverride All
        Require all granted
    </Directory>
    
    ErrorLog ${APACHE_LOG_DIR}/wardena_error.log
    CustomLog ${APACHE_LOG_DIR}/wardena_access.log combined
</VirtualHost>
```

### Nginx Configuration
```nginx
server {
    listen 80;
    server_name wardena.local;
    root /path/to/wardena-api/public;
    index index.php;

    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php8.0-fpm.sock;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
        include fastcgi_params;
    }
}
```

## اختبار التثبيت

### 1. اختبار الاتصال بقاعدة البيانات
```bash
php -r "
require 'config/config.php';
try {
    \$pdo = new PDO('mysql:host='.DB_HOST.';dbname='.DB_NAME, DB_USER, DB_PASS);
    echo 'Database connection: SUCCESS\n';
} catch(Exception \$e) {
    echo 'Database connection: FAILED - ' . \$e->getMessage() . '\n';
}
"
```

### 2. اختبار API
```bash
# اختبار endpoint أساسي
curl -X GET http://localhost/wardena-api/api/health

# يجب أن يعيد:
{"status":"ok","timestamp":"2024-01-01T00:00:00Z"}
```

### 3. اختبار تسجيل الدخول
```bash
curl -X POST http://localhost/wardena-api/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password"}'
```

## الأمان والحماية

### 1. تغيير كلمات المرور الافتراضية
```sql
-- تغيير كلمة مرور الأدمن
UPDATE users SET password = '$2y$10$your_new_hashed_password' WHERE email = '<EMAIL>';
```

### 2. تحديث المفاتيح السرية
```env
# في .env
SECRET=generate-new-secret-key-here
JWT_SECRET=generate-new-jwt-secret-here
```

### 3. إعداد SSL/TLS
```bash
# للحصول على شهادة مجانية
certbot --apache -d yourdomain.com
```

## الصيانة

### النسخ الاحتياطي
```bash
# نسخ احتياطي لقاعدة البيانات
mysqldump -u username -p wardena_db > backup_$(date +%Y%m%d).sql

# نسخ احتياطي للملفات
tar -czf files_backup_$(date +%Y%m%d).tar.gz public/uploads/
```

### تحديث النظام
```bash
# سحب آخر التحديثات
git pull origin main

# تشغيل migrations إذا وجدت
php config/database-setup.php

# مسح الكاش
rm -rf storage/cache/*
```

### مراقبة الأداء
```bash
# مراقبة ملفات السجلات
tail -f error.log

# مراقبة استخدام قاعدة البيانات
mysql -u root -p -e "SHOW PROCESSLIST;"
```

## استكشاف الأخطاء

### مشاكل شائعة:

1. **خطأ 500 Internal Server Error**
   - تحقق من error.log
   - تأكد من صلاحيات الملفات
   - تحقق من إعدادات PHP

2. **خطأ في قاعدة البيانات**
   - تحقق من معلومات الاتصال
   - تأكد من وجود قاعدة البيانات
   - تحقق من صلاحيات المستخدم

3. **مشاكل CORS**
   - تحقق من إعدادات CORS في .htaccess
   - تأكد من السماح للدومين المطلوب

### ملفات السجلات:
- `error.log` - أخطاء التطبيق
- `/var/log/apache2/error.log` - أخطاء Apache
- `/var/log/mysql/error.log` - أخطاء MySQL

## الدعم الفني

- **البريد الإلكتروني**: <EMAIL>
- **الوثائق**: https://docs.wardena.app
- **GitHub Issues**: https://github.com/your-repo/wardena-api/issues

## الترخيص

هذا المشروع مرخص تحت رخصة MIT. راجع ملف LICENSE للتفاصيل.
