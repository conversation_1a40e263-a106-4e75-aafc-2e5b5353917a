<?php

class CartController extends BaseController
{


    private $model;
    private $rates;
    private $stores;
    private $categories;
    private $products;

    public function __construct($request, $response)
    {
        parent::__construct($request, $response);
        $this->model = new DashboardModel();
        $this->rates =  new CrudModel('delivery_rates');
        $this->stores = new CrudModel('stores');
        $this->categories = new CrudModel('product_categories');
        $this->products = new CrudModel('products');
    }

    public function index()
    {

        $userId = $this->getSessionData('user_id');
        $user = $this->model->getUserData($userId);
        $rates = $this->rates->getElements();
        $fee = $this->getOurFee();

        $data = [
            'title' => 'Home',
            'style' => null,
            'user' => $user,
            'rates' => $rates,
            'fee' => $fee
        ];

        $this->render('../app/view/ui/cart.php', $data);
    }

    function getOurFee() {
        $filePath = '../config/settings.json';
        if (!file_exists($filePath)) {
            die("Error: File not found.");
        }
        $fileContent = file_get_contents($filePath);
        $data = json_decode($fileContent, true);

        if (json_last_error() !== JSON_ERROR_NONE) {
            die("Error: Invalid JSON format.");
        }
        return $data['ourFee'] ?? null;
    }
}
