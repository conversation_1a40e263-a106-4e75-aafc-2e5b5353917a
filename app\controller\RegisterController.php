<?php

class RegisterController extends BaseController {
    
    private $registerModel;

    public function __construct($request, $response) {
        parent::__construct($request, $response);
        $this->registerModel = new RegisterModel();
    }

    public function index() {
        if ($this->isLoggedIn()) {
            $this->redirect('/home');
            return;
        }

        if ($this->isPost()) {
            $this->handleRegistration();
        } else {
            $data = [
                'title' => 'Register',
            ];
            $this->render('../app/views/user/register.php', $data);
        }
    }

    private function handleRegistration() {
        $fields = ['name', 'last_name', 'company', 'mobile', 'country', 'state', 'city', 'postal_code', 'address1', 'address2', 'email', 'username', 'password', 'confirm_password', 'type'];
        $formData = $this->getFormData($fields);
        $validationErrors = $this->validateFormData($formData);

        if (!empty($validationErrors)) {
            $data = [
                'title' => 'Error Reg',
                'errors' => $validationErrors,
                'old' => $formData,
            ];
            $this->render('../app/views/user/register.php', $data);
            return;
        }

        $hashedPassword = password_hash($formData['password'], PASSWORD_DEFAULT);
        $formData['password'] = $hashedPassword;

        if ($this->registerModel->register($formData)) {
            $this->setAlert('Registration successful!', 'success');
            $this->redirect('/login');
        } else {
            $this->setAlert('There was a problem during registration. Please try again!', 'error');
        }
    }

    private function validateFormData($data) {
        $errors = [];

        $requiredFields = ['name', 'last_name', 'email', 'username', 'password', 'confirm_password', 'mobile'];
        foreach ($requiredFields as $field) {
            if (empty($data[$field])) {
                $errors[$field] = $this->formatFieldName($field) . ' is required!';
            }
        }

        if (!filter_var($data['email'], FILTER_VALIDATE_EMAIL)) {
            $errors['email'] = 'Invalid email address!';
        } elseif ($this->registerModel->emailExists($data['email'])) {
            $errors['email'] = 'Email is already registered.';
        }

        if ($this->registerModel->usernameExists($data['username'])) {
            $errors['username'] = 'Username is already taken.';
        }

        if ($data['password'] !== $data['confirm_password']) {
            $errors['confirm_password'] = 'Passwords do not match!';
        }

        // Password strength check
        if (strlen($data['password']) < 8) {
            $errors['password'] = 'Password must be at least 8 characters long!';
        }

        if (!preg_match('/[a-z]/', $data['password'])) {
            $errors['password'] = 'Password must contain at least one lowercase letter!';
        }

        if (!preg_match('/[A-Z]/', $data['password'])) {
            $errors['password'] = 'Password must contain at least one uppercase letter!';
        }

        if (!preg_match('/[0-9]/', $data['password'])) {
            $errors['password'] = 'Password must contain at least one number!';
        }

        if (!preg_match('/[!@#$%^&*()\-_=+{};:,<.>]/', $data['password'])) {
            $errors['password'] = 'Password must contain at least one special character!';
        }

        return $errors;
    }

}
