<?php

class HomeModel extends BaseModel {

    // Constructor sets the table name
    public function __construct() {
        $this->table = 'posts';  // Assuming 'posts' is the main table for this model
    }

    // Get the latest posts for the homepage
    public function getLatestPosts($limit = 5) {
        $stmt = $this->db->prepare("SELECT * FROM {$this->table} ORDER BY created_at DESC LIMIT :limit");
        $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    // Get the featured item for the homepage
    public function getFeaturedItem() {
        // This is a mockup; you might have a different mechanism for determining featured items
        $stmt = $this->db->prepare("SELECT * FROM {$this->table} WHERE is_featured = 1 LIMIT 1");
        $stmt->execute();
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    // Get information for the about page
    public function getAboutInfo() {
        // Assuming there's an 'about' table in the database
        $stmt = $this->db->prepare("SELECT * FROM about LIMIT 1");
        $stmt->execute();
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    // Additional methods related to the home model can go here...

}

?>
