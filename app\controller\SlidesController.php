<?php

class SlidesController extends BaseController
{

    private $model;
    private $users;
    private $val;

    public function __construct($request, $response)
    {
        parent::__construct($request, $response);
        $this->model = new CrudModel('slides');
        $this->users = new CrudModel('users');
        $this->val = new ValidateModel('slides');
    }

    public function index()
    {

        $data = $this->model->getElements();
        header('Content-Type: application/json');
        echo json_encode($data);

    }


    public function new()
    {
        if ($this->isLoggedIn()) {

            if ($this->isPost()) {
                $this->handleNewSlide();
            }
        }
    }


    private function handleNewSlide()
    {
        $fields = ['title', 'heading', 'text', 'url'];
        $formData = $this->getFormData($fields);
        $validationErrors = $this->validateFormData($formData);

        if (!empty($validationErrors)) {
            $data = [
                'errors' => $validationErrors,
                'old' => $formData,
            ];
            $this->redirect('/settings/slides', $data);
            $this->logError('Failed to validate slide form data');
            return;
        }

        if (isset($_FILES['file']) && is_uploaded_file($_FILES['file']['tmp_name'])) {

            $file = $_FILES['file'];

            if (!isset($file['error']) || is_array($file['error'])) {
                throw new Exception("Invalid file parameters.");
            }

            if ($file['error'] !== UPLOAD_ERR_OK) {
                throw new Exception("File upload error: " . $this->fileUploadErrorMessage($file['error']));
            }
            $fileName = basename($file['name']);

            $uploadDir = __DIR__ . '/../../public/uploads/slides/';
            if (!is_dir($uploadDir)) {
                mkdir($uploadDir, 0777, true);
            }
            $fileName = $this->getUniqueFileName($uploadDir, $fileName);
            $uploadFile = $uploadDir . $fileName;
            $fileType = mime_content_type($file['tmp_name']);
            $fileSize = $file['size'];

            if (!move_uploaded_file($file['tmp_name'], $uploadFile)) {
                throw new Exception("File upload failed.");
            }

            if (!$this->validateFile($fileType, $fileSize)) {
                unlink($uploadFile);
                throw new Exception("File validation failed.");
            }

            $img = $fileName;
            $formData['image'] = $img;

        }

        if ($this->model->createElement($formData)) {
            $this->redirect('/settings/slides');
        } else {
            $this->setAlert('There was a problem during adding slide. Please try again!', 'error');
        }
    }

    public function update()
    {
        try {
            $this->model->beginTransaction();

            $id = $_GET['id'];
            $fields = ['title', 'heading', 'text', 'url'];
            $formData = $this->getFormData($fields);


            if (isset($_FILES['file']) && is_uploaded_file($_FILES['file']['tmp_name'])) {
                $file = $_FILES['file'];

                if (!isset($file['error']) || is_array($file['error'])) {
                    throw new Exception("Invalid file parameters.");
                }

                if ($file['error'] !== UPLOAD_ERR_OK) {
                    throw new Exception("File upload error: " . $this->fileUploadErrorMessage($file['error']));
                }
                $fileName = basename($file['name']);

                $uploadDir = __DIR__ . '/../../public/uploads/slides/';
                if (!is_dir($uploadDir)) {
                    mkdir($uploadDir, 0777, true);
                }
                $fileName = $this->getUniqueFileName($uploadDir, $fileName);
                $uploadFile = $uploadDir . $fileName;
                $fileType = mime_content_type($file['tmp_name']);
                $fileSize = $file['size'];

                if (!move_uploaded_file($file['tmp_name'], $uploadFile)) {
                    throw new Exception("File upload failed.");
                }

                if (!$this->validateFile($fileType, $fileSize)) {
                    unlink($uploadFile);
                    throw new Exception("File validation failed.");
                }

                $img = $fileName;
                $formData['image'] = $img;
            }

            if (!$this->model->updateElement($formData, $id)) {
                $this->model->rollBack();
                return false;
            } else {
                $this->model->commit();
                $this->redirect('/settings/slides');
                return;
            }
        } catch (Exception $e) {
            $this->model->rollBack();
            $this->logError($e->getMessage());
            return $this->redirect('/error');
        }
    }

    public function delete()
    {
        try {
            $this->model->beginTransaction();

            $id = $_GET['id'];

            if (!$this->model->delete($id)) {
                $this->model->rollBack();
                return false;
            } else {
                $this->model->commit();
                $this->redirect('//settings/slides');
                return;
            }
        } catch (Exception $e) {
            $this->model->rollBack();
            $this->logError($e->getMessage());
            return $this->redirect('/error');
        }
    }

    private function validateFormData($data)
    {
        $errors = [];
        $requiredFields = ['title'];

        foreach ($requiredFields as $field) {
            if (empty($data[$field])) {
                $errors[$field] = $this->formatFieldName($field) . ' is required!';
            }
        }

        if ($this->val->elementExists('title', $data['title'])) {
            $errors['title'] = 'this category is already exist.';
        }

        return $errors;
    }
}
