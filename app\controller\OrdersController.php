<?php

class OrdersController extends BaseController
{

    private $model;
    private $orderDetails;
    private $auth;
    private $users;
    private $service;


    public function __construct($request, $response)
    {
        parent::__construct($request, $response);
        $this->model = new CrudModel('orders');
        $this->orderDetails = new CrudModel('order_details');
        $this->service = new OrderService();
        $this->auth = new AuthService();
        $this->users = new CrudModel('users');
    }

    public function index()
    {
        if ($this->auth->admin() == true || $this->auth->employee() == true) {
            $userId = $this->getSessionData('user_id');
            $user = $this->users->getElement($userId);
            $delivery = $this->users->getElementsWhere('role', 'delivery_boy');
            $result = $this->service->getOrders();
            $data = [
                'title' => 'Orders',
                'style' => null,
                'user' => $user,
                'delivery' => $delivery,
                'result' => $result,
            ];

            $this->render('../app/view/admin/orders.php', $data);
        } else {
            $this->redirect('/login');
        }
    }

    public function getOrder()
    {
        if ($this->auth->admin() == true || $this->auth->employee() == true || $this->auth->delivery() == true || $this->auth->owner() == true) {
            $id = $_GET['id'];
            $result = $this->service->getOrder($id);

            http_response_code(200);
            echo json_encode(['message' => 'Order placed successfully.', 'data' => $result]);
        } else {
            $this->redirect('/login');
        }
    }

    public function placeOrder() {
        try {

            $this->model->beginTransaction();

            $rawInput = file_get_contents('php://input');
            $this->logError("Raw Input: " . $rawInput);
            $input = json_decode($rawInput, true);
            
            if (json_last_error() !== JSON_ERROR_NONE) {
                $this->logError('Error Invalid JSON input');

                http_response_code(400);
                echo json_encode(['error' => 'Invalid JSON input: ' . json_last_error_msg()]);
                return;
            }

            if (!$input) {
                $this->logError("Invalid input: $rawInput");

                http_response_code(400);
                echo json_encode(['error' => 'Invalid JSON input.']);
                return;
            }
            
            if (!isset($input['form'])) {
                $this->logError('Missing "form" data.');

                http_response_code(400);
                echo json_encode(['error' => 'Missing "form" data.']);
                return;
            }
            
            if (!isset($input['cart'])) {
                $this->logError('Missing "cart" data.');

                http_response_code(400);
                echo json_encode(['error' => 'Missing "cart" data.']);
                return;
            }
            
            $requiredFields = ['name', 'phone', 'address'];
            foreach ($requiredFields as $field) {
                if (empty($input['form'][$field])) {
                    $this->logError('Missing required field: '. $field);

                    http_response_code(400);
                    echo json_encode(['error' => "Missing required field: $field"]);
                    return;
                }
            }
        
            $name = $input['form']['name'];
            $phone = $input['form']['phone'];
            $password = $input['form']['password'] ?? ''; // Optional field
            $state = $input['form']['state'] ?? '';       // Optional field
            $city = $input['form']['city'] ?? '';         // Optional field
            $address = $input['form']['address'];
            $note = $input['form']['note'] ?? '';
            $cart = $input['cart'];
            $shippingFee = $input['shippingFee'] ?? 0; // Default to 0 if not provided
            $subtotal = $input['subtotal'] ?? 0;      // Default to 0 if not provided
        
            $hashedPassword = $password ? password_hash($password, PASSWORD_DEFAULT) : null;
        
            $userData = [
                'password' => $hashedPassword,
                'role' => 'customer',
                'name' => $name,
                'phone' => $phone,
                'country' => 'العراق',
                'state' => $state,
                'city' => $city,
                'address' => $address,
            ];
        
            $userId = $this->users->createElementGetId($userData);
            if (!$userId) {
                $this->logError('Failed to save user information.');
                $this->model->rollBack();

                http_response_code(500);
                echo json_encode(['error' => 'Failed to save user information.']);
                return;
            }
            $total_amount = 0;

            foreach ($cart as $productId => $product) {
                $price = isset($product['price']) ? (float) $product['price'] : 0;
                $quantity = isset($product['quantity']) ? (int) $product['quantity'] : 0;
                $total_amount += $price * $quantity;
            }
        
            $orderData = [
                'customer_id' => $userId,
                'total_amount' => $total_amount,
                'delivery_fee' => $shippingFee
            ];
        
            $orderId = $this->model->createElementGetId($orderData);
            if (!$orderId) {
                $this->logError('Order placed successfully.');
                $this->model->commit();

                http_response_code(200);
                echo json_encode(['message' => 'Order placed successfully.', 'orderId' => $orderId]);

            } else {
                foreach ($cart as $productId => $product) {

                    $name = $product['name'];
                    $price = $product['price'];
                    $quantity = $product['quantity'];
                    $total_price = $quantity * $price;
    
                    $orderDetails = [
                        'order_id' => $orderId,
                        'product_id' => $productId,
                        'store_id' => 0,
                        'quantity' => $quantity,
                        'price' => $price,
                        'total_price' => $total_price
                    ];

                    if (!$this->orderDetails->createElement($orderDetails)) {
                        $this->logError('Failed to place the order.');
                        $this->model->rollBack();
        
                        http_response_code(500);
                        echo json_encode(['error' => 'Failed to place the order.']);
                        return;
                    }
                
                }
            }

            $this->model->commit();
            http_response_code(200);
            echo json_encode(['message' => 'Order placed successfully.', 'orderId' => $orderId]);

        } catch (Exception $e) {
            $this->logError($e->getMessage());
            $this->model->rollBack();
            return $this->redirect('/error');
        }
    }

    public function update()
    {
        try {
            $this->model->beginTransaction();

            $rawInput = file_get_contents('php://input');
            $this->logError("Raw Input: " . $rawInput);
            $input = json_decode($rawInput, true);
            
            if (json_last_error() !== JSON_ERROR_NONE) {
                $this->logError('Error Invalid JSON input');

                http_response_code(400);
                echo json_encode(['error' => 'Invalid JSON input: ' . json_last_error_msg()]);
                return;
            }

            if (!$input) {
                $this->logError("Invalid input: $rawInput");

                http_response_code(400);
                echo json_encode(['error' => 'Invalid JSON input.']);
                return;
            }

            $id = $input['form']['order_id'];

            $formData = [
                'status' => $input['form']['status']
            ];

            if (!$this->model->updateElement($formData, $id)) {
                $this->model->rollBack();
                http_response_code(500);
                echo json_encode(['error' => 'Failed to change status.']);
                return false;
            } else {
                $this->model->commit();
                http_response_code(200);
                echo json_encode(['message' => 'Order status changed successfully.']);
            }
        } catch (Exception $e) {
            $this->model->rollBack();
            $this->logError($e->getMessage());
            return $this->redirect('/error');
        }
    }

    public function assign()
    {
        try {
            $this->model->beginTransaction();

            $rawInput = file_get_contents('php://input');
            $this->logError("Raw Input: " . $rawInput);
            $input = json_decode($rawInput, true);
            
            if (json_last_error() !== JSON_ERROR_NONE) {
                $this->logError('Error Invalid JSON input');

                http_response_code(400);
                echo json_encode(['error' => 'Invalid JSON input: ' . json_last_error_msg()]);
                return;
            }

            if (!$input) {
                $this->logError("Invalid input: $rawInput");

                http_response_code(400);
                echo json_encode(['error' => 'Invalid JSON input.']);
                return;
            }

            $id = $input['form']['order_id'];

            $formData = [
                'delivery_boy_id' => $input['form']['delivery']
            ];

            if (!$this->model->updateElement($formData, $id)) {
                $this->model->rollBack();
                http_response_code(500);
                echo json_encode(['error' => 'Failed to assign driver.']);
                return false;
            } else {
                $this->model->commit();
                http_response_code(200);
                echo json_encode(['message' => 'Order driver assigned successfully.']);
            }
        } catch (Exception $e) {
            $this->model->rollBack();
            $this->logError($e->getMessage());
            return $this->redirect('/error');
        }
    }

    public function delete()
    {
        try {
            $this->model->beginTransaction();

            $id = $_GET['id'];

            if (!$this->model->delete($id)) {
                $this->model->rollBack();
                return false;
            } else {
                $this->model->commit();
                $this->redirect('/orders');
                return;
            }
        } catch (Exception $e) {
            $this->model->rollBack();
            $this->logError($e->getMessage());
            return $this->redirect('/error');
        }
    }
    

}
