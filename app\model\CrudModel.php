<?php

class CrudModel extends BaseModel
{
    protected $table;

    public function __construct($table)
    {
        parent::__construct();
        $this->table = $table;
    }

    public function createElement(array $data)
    {
        return $this->insert($data);
    }
    
    public function createElementGetId(array $data)
    {
        return $this->insertReturnId($data);
    }

    public function updateElement(array $data, $id)
    {
        return $this->update($id, $data);
    }

    public function getElement($id)
    {
        return $this->getById($id);
    }

    public function getElements()
    {
        return $this->getAll();
    }

    public function getFiltered($filters, $pagination, $sorting){
        $result = $this->filter($filters, $pagination, $sorting);
        return $result;
    }

    public function getElementsWhere($col, $x)
    {
        return $this->getWhere($col, $x);
    }

    public function deleteElement($id)
    {
        $data = $this->delete($id);
        return $data;
    }
    
    /**
     * Execute a raw SQL query (DELETE, UPDATE, INSERT, etc.).
     * @param string $query - The SQL query to execute.
     * @param array $params - Optional array of parameters for prepared statements.
     * @return bool|int - Returns true on success for DELETE/UPDATE, or false on failure.
     */
    public function executeQuery($query, $params = [])
    {
        try {
            $stmt = $this->db->prepare($query);
            $result = $stmt->execute($params);
            return $result;
        } catch (Exception $e) {
            error_log("Query Execution Error: " . $e->getMessage());
            return false;
        }
    }


}