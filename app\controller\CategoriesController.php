<?php

class CategoriesController extends BaseController
{

    private $model;
    private $users;
    private $val;

    public function __construct($request, $response)
    {
        parent::__construct($request, $response);
        $this->model = new CrudModel('product_categories');
        $this->users = new CrudModel('users');
        $this->val = new ValidateModel('product_categories');
    }

    public function index()
    {
        if ($this->isLoggedIn()) {
            $userId = $this->getSessionData('user_id');
            $user = $this->users->getElement($userId);
            $result = $this->model->getElements();
            $hierarchicalCategories = $this->buildHierarchy($result);
            $flatCategories = $this->flattenHierarchy($hierarchicalCategories);

            $data = [
                'title' => 'Categories',
                'style' => null,
                'user' => $user,
                'result' => $result,
                'flatCategories' => $flatCategories,
            ];
            $this->render('../app/view/admin/categories.php', $data);
        } else {
            $this->redirect('/login');
        }
    }


    public function newCategory()
    {
        if ($this->isLoggedIn()) {
            if ($this->isPost()) {
                $this->handleNewCategory();
            } else {
                $userId = $this->getSessionData('user_id');
                $user = $this->model->getElement($userId);
                $data = [
                    'title' => 'Categories',
                    'style' => null,
                    'user' => $user
                ];

                $this->render('../app/view/admin/categories.php', $data);
            }
        } else {
            echo "error";
        }
    }


    private function handleNewCategory()
    {
        $fields = ['name', 'slug', 'parent'];
        $formData = $this->getFormData($fields);
        $validationErrors = $this->validateFormData($formData);

        if (!empty($validationErrors)) {
            $data = [
                'errors' => $validationErrors,
                'old' => $formData,
            ];
            $this->redirect('/categories', $data);
            $this->logError('Failed to validate category form data');
            return;
        }

        if (isset($_FILES['file']) && is_uploaded_file($_FILES['file']['tmp_name'])) {

            $file = $_FILES['file'];

            if (!isset($file['error']) || is_array($file['error'])) {
                throw new Exception("Invalid file parameters.");
            }

            if ($file['error'] !== UPLOAD_ERR_OK) {
                throw new Exception("File upload error: " . $this->fileUploadErrorMessage($file['error']));
            }
            $fileName = basename($file['name']);

            $uploadDir = __DIR__ . '/../../public/uploads/categories/';
            if (!is_dir($uploadDir)) {
                mkdir($uploadDir, 0777, true);
            }
            $fileName = $this->getUniqueFileName($uploadDir, $fileName);
            $uploadFile = $uploadDir . $fileName;
            $fileType = mime_content_type($file['tmp_name']);
            $fileSize = $file['size'];

            if (!move_uploaded_file($file['tmp_name'], $uploadFile)) {
                throw new Exception("File upload failed.");
            }

            if (!$this->validateFile($fileType, $fileSize)) {
                unlink($uploadFile);
                throw new Exception("File validation failed.");
            }

            $img = $fileName;
            $formData['img'] = $img;

        }

        if ($this->model->createElement($formData)) {
            $this->redirect('/categories');
        } else {
            $this->setAlert('There was a problem during registration. Please try again!', 'error');
        }
    }

    public function update()
    {
        try {
            $this->model->beginTransaction();

            $id = $_GET['id'];
            $fields = ['name', 'slug', 'parent'];
            $formData = $this->getFormData($fields);


            if (isset($_FILES['file']) && is_uploaded_file($_FILES['file']['tmp_name'])) {
                $file = $_FILES['file'];

                if (!isset($file['error']) || is_array($file['error'])) {
                    throw new Exception("Invalid file parameters.");
                }

                if ($file['error'] !== UPLOAD_ERR_OK) {
                    throw new Exception("File upload error: " . $this->fileUploadErrorMessage($file['error']));
                }
                $fileName = basename($file['name']);

                $uploadDir = __DIR__ . '/../../public/uploads/categories/';
                if (!is_dir($uploadDir)) {
                    mkdir($uploadDir, 0777, true);
                }
                $fileName = $this->getUniqueFileName($uploadDir, $fileName);
                $uploadFile = $uploadDir . $fileName;
                $fileType = mime_content_type($file['tmp_name']);
                $fileSize = $file['size'];

                if (!move_uploaded_file($file['tmp_name'], $uploadFile)) {
                    throw new Exception("File upload failed.");
                }

                if (!$this->validateFile($fileType, $fileSize)) {
                    unlink($uploadFile);
                    throw new Exception("File validation failed.");
                }

                $img = $fileName;
                $formData['img'] = $img;
            }

            if (!$this->model->updateElement($formData, $id)) {
                $this->model->rollBack();
                return false;
            } else {
                $this->model->commit();
                $this->redirect('/categories');
                return;
            }
        } catch (Exception $e) {
            $this->model->rollBack();
            $this->logError($e->getMessage());
            return $this->redirect('/error');
        }
    }

    public function delete()
    {
        try {
            $this->model->beginTransaction();

            $id = $_GET['id'];

            if (!$this->model->delete($id)) {
                $this->model->rollBack();
                return false;
            } else {
                $this->model->commit();
                $this->redirect('/categories');
                return;
            }
        } catch (Exception $e) {
            $this->model->rollBack();
            $this->logError($e->getMessage());
            return $this->redirect('/error');
        }
    }

    private function validateFormData($data)
    {
        $errors = [];
        $requiredFields = ['name'];

        foreach ($requiredFields as $field) {
            if (empty($data[$field])) {
                $errors[$field] = $this->formatFieldName($field) . ' is required!';
            }
        }

        if ($this->val->elementExists('name', $data['name'])) {
            $errors['name'] = 'this category is already exist.';
        }

        return $errors;
    }
}
