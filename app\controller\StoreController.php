<?php

class StoreController extends BaseController
{

    private $model;
    private $users;
    private $auth;
    private $products;
    private $orders;
    private $service;
    private $productService;
    private $categories;

    private $brands;
    private $stores;
    private $valProduct;

    private $orderService;

    public function __construct($request, $response)
    {
        parent::__construct($request, $response);
        $this->model = new CrudModel('stores');
        $this->products = new CrudModel('products');
        $this->users = new CrudModel('users');
        $this->orders = new CrudModel('orders');
        $this->auth = new AuthService();
        $this->service = new StoreService();
        $this->productService = new ProductService();
        $this->categories = new CrudModel('product_categories');

        $this->brands = new CrudModel('brands');
        $this->stores = new CrudModel('stores');
        $this->valProduct = new ValidateModel('products');

        $this->orderService = new OrderService();

    }

    public function index()
    {
        if ($this->auth->owner() == true) {
            $userId = $this->getSessionData('user_id');
            $user = $this->users->getElement($userId);
            $store = $this->model->getElementsWhere('owner_id', $userId);

            $products = $this->products->getElementsWhere('store_id', $store[0]['id']);
            $productsCount = count($products);

            $orders = $this->orderService->getStoreOrders($store[0]['id']);
            $ordersCount = count($orders);

            $count = [
                'products' => $productsCount,
                'orders' => $ordersCount,
            ];
            $data = [
                'title' => 'Store Admin Dashboard',
                'style' => null,
                'user' => $user,
                'store' => $store,
                'count' => $count
            ];

            $this->render('../app/view/store/dashboard.php', $data);
        } else {
            $this->redirect('/login');
        }
    }

    public function orders()
    {
        if ($this->auth->owner() == true) {
            $userId = $this->getSessionData('user_id');
            $user = $this->users->getElement($userId);
            $store = $this->model->getElementsWhere('owner_id', $userId);
            $store_id = $store[0]['id'];
            $result = $this->orderService->getStoreOrders($store_id);
            $data = [
                'title' => 'Store Admin Dashboard',
                'style' => null,
                'user' => $user,
                'store' => $store,
                'delivery' => $delivery,
                'result' => $result,
            ];
            $this->render('../app/view/store/orders.php', $data);
        } else {
            $this->redirect('/login');
        }
    }

    public function getOrder()
    {
        if ($this->auth->owner() == true) {

            $userId = $this->getSessionData('user_id');
            $user = $this->users->getElement($userId);
            $store = $this->model->getElementsWhere('owner_id', $userId);
            $store_id = $store[0]['id'];

            $id = $_GET['id'];
            $result = $this->orderService->getStoreOrder($id, $store_id);

            http_response_code(200);
            echo json_encode(['message' => 'Order placed successfully.', 'data' => $result]);
        } else {
            $this->redirect('/login');
        }
    }

    public function getProduct() {

        $id = $_GET['id'];
        $product = $this->products->getElement($id);
        $formattedPrice = number_format($product['price'], 0);
        $product['price'] = $formattedPrice;

        echo json_encode($product);
    }

    public function products()
    {
        if ($this->auth->owner() == true) {
            $userId = $this->getSessionData('user_id');
            $user = $this->users->getElement($userId);

            $store = $this->model->getElementsWhere('owner_id', $userId);
            $store_id = $store[0]['id'];

            $filters = ['category_id' => $_GET['category_id'] ?? null, 'store_id' => $store_id];
            $pagination = ['page' => $_GET['page'] ?? 1, 'limit' => 25];

            $sortOption = $_GET['sort'] ?? null;
            switch ($sortOption) {
                case 'price_asc':
                    $sorting = ['column' => 'price', 'order' => 'ASC'];
                    break;
                case 'price_desc':
                    $sorting = ['column' => 'price', 'order' => 'DESC'];
                    break;
                default:
                    $sorting = ['column' => 'price', 'order' => 'ASC'];
            }

            $filtered = $this->productService->getFilteredProducts($filters, $pagination, $sorting);
            $result = $filtered['products'];

            $total = $filtered['total'];
            $totalPages = ceil($total / $pagination['limit']);

            $categories = $this->categories->getElements();
            
            $hierarchicalCategories = $this->buildHierarchy($categories);
            $flatCategories = $this->flattenHierarchy($hierarchicalCategories);

            $data = [
                'title' => 'Store Admin Dashboard',
                'style' => null,
                'user' => $user,
                'store' => $store,
                'result' => $result,
                'totalPages' => $totalPages,
                'categories' => $flatCategories,
                'currentPage' => $pagination['page'],
            ];

            $this->render('../app/view/store/products.php', $data);
        } else {
            $this->redirect('/login');
        }
    }

    public function newProduct()
    {
        if ($this->auth->owner() == true) {
            if ($this->isPost()) {
                $this->handelNewProduct();
            }
            $userId = $this->getSessionData('user_id');
            $user = $this->users->getElement($userId);

            $store = $this->model->getElementsWhere('owner_id', $userId);
            $store_id = $store[0]['id'];

            $brands = $this->brands->getElements();
            $categories = $this->categories->getElements();
            $hierarchicalCategories = $this->buildHierarchy($categories);
            $flatCategories = $this->flattenHierarchy($hierarchicalCategories);



            $data = [
                'title' => 'Products',
                'style' => null,
                'user' => $user,
                'store' => $store,
                'store_id' => $store_id,
                'brands' => $brands,
                'categories' => $flatCategories
            ];

            $this->render('../app/view/store/new-product.php', $data);
        } else {
            $this->redirect('/login');
        }
    }

    public function handelNewProduct()
    {
        try {

            $this->products->beginTransaction();

            $fields = ['store_id', 'category_id', 'brand_id', 'name', 'description', 'price', 'app_percentage'];
            $formData = $this->getFormData($fields);
            $required = ['store_id', 'category_id', 'brand_id', 'name', 'price', 'app_percentage'];
            $validationErrors = $this->valProduct->required($formData, $required);

            if (!empty($validationErrors)) {
                $data = [
                    'errors' => $validationErrors,
                    'old' => $formData,
                ];
                $this->redirect('/store/new-product', $data);
                $this->logError('Failed to validate');

                return;
            }

            $file = $_FILES['file'];

            if (!isset($file['error']) || is_array($file['error'])) {

                $validationErrors['file'] = "Invalid file parameters.";
                $data = [
                    'errors' => $validationErrors,
                    'old' => null,
                ];
                $this->redirect('/store/new-product', $data);
            }

            if ($file['error'] !== UPLOAD_ERR_OK) {
                $validationErrors['file'] = "File upload error: " . $this->fileUploadErrorMessage($file['error']);
                $data = [
                    'errors' => $validationErrors,
                    'old' => null,
                ];
                $this->redirect('/store/new-product', $data);
            }
            $fileName = basename($file['name']);

            $uploadDir = __DIR__ . '/../../public/uploads/products/' . $formData['store_id'] . '/';
            if (!is_dir($uploadDir)) {
                mkdir($uploadDir, 0777, true);
            }
            $fileName = $this->getUniqueFileName($uploadDir, $fileName);
            $uploadFile = $uploadDir . $fileName;
            $fileType = mime_content_type($file['tmp_name']);
            $fileSize = $file['size'];

            if (!move_uploaded_file($file['tmp_name'], $uploadFile)) {

                $validationErrors['file'] = "File upload failed.";
                $data = [
                    'errors' => $validationErrors,
                    'old' => null,
                ];
                $this->redirect('/store/new-product', $data);
            }

            if (!$this->validateFile($fileType, $fileSize)) {
                unlink($uploadFile);

                $validationErrors['file'] = "File validation failed.";
                $data = [
                    'errors' => $validationErrors,
                    'old' => null,
                ];
                $this->redirect('/store/new-product', $data);
            }

            $img = $fileName;
            $formData['image'] = $img;

            if (!$this->products->createElement($formData)) {
                $this->logError('Failed to create store');
                $this->products->rollBack();
            } else {
                $this->products->commit();
                $with = ['response' => 'success'];
                $this->redirect('/store/products', $with);
                return;
            }
        } catch (Exception $e) {
            $this->products->rollBack();
            $this->logError($e->getMessage());
            return $this->redirect('/store/new-product');
        }
    }

    public function editProduct()
    {
        if ($this->auth->owner() == true) {
            if ($this->isPost()) {
                $this->updateProduct();
            } else {
                $userId = $this->getSessionData('user_id');
                $user = $this->users->getElement($userId);
                $id = $_GET['id'];
                $product = $this->products->getElement($id);

                $store = $this->model->getElementsWhere('owner_id', $userId);
                $store_id = $store[0]['id'];

                $brands = $this->brands->getElements();
                $categories = $this->categories->getElements();

                $hierarchicalCategories = $this->buildHierarchy($categories);
                $flatCategories = $this->flattenHierarchy($hierarchicalCategories);

                $data = [
                    'title' => 'Products',
                    'style' => null,
                    'user' => $user,
                    'store' => $store,
                    'store_id' => $store_id,
                    'brands' => $brands,
                    'categories' => $flatCategories,
                    'product' => $product
                ];

                $this->render('../app/view/store/edit-product.php', $data);
            }
        } else {
            $this->redirect('/login');
        }
    }

    public function updateProduct()
    {
        try {

            $this->products->beginTransaction();

            $fields = ['id', 'store_id', 'category_id', 'brand_id', 'name', 'description', 'price', 'app_percentage'];
            $formData = $this->getFormData($fields);
            $id = $formData['id'];

            $required = ['store_id', 'category_id', 'brand_id', 'name', 'price', 'app_percentage'];
            $validationErrors = $this->valProduct->required($formData, $required);

            if (!empty($validationErrors)) {
                $data = [
                    'errors' => $validationErrors,
                    'old' => $formData,
                ];
                $this->redirect('/store/edit-product?id=' . $id, $data);
                $this->logError('Failed to validate');

                return;
            }

            if (!$this->products->updateElement($formData, $id)) {
                $this->logError('Failed to create store');
                $this->products->rollBack();
            } else {
                $this->products->commit();
                $with = ['response' => 'success'];
                $this->redirect('/store/edit-product?id=' . $id, $with);
                return;
            }
        } catch (Exception $e) {
            $this->products->rollBack();
            $this->logError($e->getMessage());
            return $this->redirect('/store/edit-product?id=' . $id);
        }
    }

    public function updateProductImg()
    {

        try {

            $this->products->beginTransaction();
            $fields = ['store_id', 'id'];
            $formData = $this->getFormData($fields);
            $id = $formData['id'];

            $file = $_FILES['file'];

            if (!isset($file['error']) || is_array($file['error'])) {

                $validationErrors['file'] = "Invalid file parameters.";
                $data = [
                    'errors' => $validationErrors,
                    'old' => null,
                ];
                $this->redirect('/store/edit-product?id=' . $id, $data);
            }

            if ($file['error'] !== UPLOAD_ERR_OK) {
                $validationErrors['file'] = "File upload error: " . $this->fileUploadErrorMessage($file['error']);
                $data = [
                    'errors' => $validationErrors,
                    'old' => null,
                ];
                $this->redirect('/store/edit-product?id=' . $id, $data);
            }
            $fileName = basename($file['name']);

            $uploadDir = __DIR__ . '/../../public/uploads/products/' . $formData['store_id'] . '/';
            if (!is_dir($uploadDir)) {
                mkdir($uploadDir, 0777, true);
            }
            $fileName = $this->getUniqueFileName($uploadDir, $fileName);
            $uploadFile = $uploadDir . $fileName;
            $fileType = mime_content_type($file['tmp_name']);
            $fileSize = $file['size'];

            if (!move_uploaded_file($file['tmp_name'], $uploadFile)) {

                $validationErrors['file'] = "File upload failed.";
                $data = [
                    'errors' => $validationErrors,
                    'old' => null,
                ];
                $this->redirect('/store/edit-product?id=' . $id, $data);
            }

            if (!$this->validateFile($fileType, $fileSize)) {
                unlink($uploadFile);

                $validationErrors['file'] = "File validation failed.";
                $data = [
                    'errors' => $validationErrors,
                    'old' => null,
                ];
                $this->redirect('/store/edit-product?id=' . $id, $data);
            }

            $img = $fileName;
            $data['image'] = $img;


            if (!$this->products->updateElement($data, $id)) {
                $this->logError('Failed to udate product image');
                $this->products->rollBack();
            } else {
                $this->products->commit();
                $with = ['response' => 'success'];
                $this->redirect('/store/edit-product?id=' . $id, $with);
                return;
            }
        } catch (Exception $e) {
            $this->products->rollBack();
            $this->logError($e->getMessage());
            return $this->redirect('/store/edit-product?id=' . $id);
        }
    }

    public function deleteProduct()
    {
        try {
            $this->products->beginTransaction();
            $id = $_GET['id'];

            if (!$this->products->deleteElement($id)) {
                $this->products->rollBack();
                return false;
            } else {
                $this->products->commit();
                $this->redirect('/store/products');
                return;
            }
        } catch (Exception $e) {
            $this->products->rollBack();
            $this->logError($e->getMessage());
            return $this->redirect('/error');
        }
    }
}
