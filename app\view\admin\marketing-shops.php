<!--start content-->
<main class="page-content">
    <!--breadcrumb-->
    <div class="page-breadcrumb d-none d-sm-flex align-items-center mb-3">
        <a class="breadcrumb-title pe-3" href="/marketing">التسويق</a>
        <div class="ps-3">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb mb-0 p-0">
                    <li class="breadcrumb-item"><a href="javascript:;"><i class="bx bx-home-alt"></i></a>
                    </li>
                    <li class="breadcrumb-item active" aria-current="page">تسويق المتاجر</li>
                </ol>
            </nav>
        </div>
    </div>
    <!--end breadcrumb-->
    <div class="card">
        <div class="card-header py-3">
            <h6 class="mb-0">المتاجر</h6>
        </div>
        
        <div class="card-body">
            <div class="row">
                <div class="col-12 col-lg-12 d-flex">
                    <div class="card border shadow-none w-100">
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table align-middle">
                                    <thead class="table-light">
                                        <tr>
                                            <th>الرقم</th>
                                            <th>المتجر</th>
                                        </tr>
                                    </thead>
                                    <tbody id="sortable">
                                        <?php foreach ($shops as $shop): ?>
                                            <tr data-id="<?= $shop['id'] ?>">
                                                <td><?= $shop['ordering'] ?></td>
                                                <td><?= $shop['name'] ?></td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div><!--end row-->
        </div>
    </div>

</main>
<!--end page main-->
<?php 
$jsFiles = '
<script src="/assets/plugins/sortable/sortable.js"></script>
<script>
    new Sortable(document.getElementById("sortable"), {
        animation: 150,
        onEnd: function (evt) {
            let order = [];
            document.querySelectorAll("#sortable tr").forEach((row, index) => {
                order.push({ id: row.dataset.id, order: index + 1 });
            });

            fetch("/marketing/update-shops-order", {
                method: "POST",
                headers: { "Content-Type": "application/json" },
                body: JSON.stringify(order),
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === "success") {
                    console.log("Order updated successfully");
                } else {
                    console.error("Error:", data.message);
                    alert("Failed to update order. Try again.");
                }
            })
            .catch(error => console.error("Error:", error));
        }
    });

</script>
';
?>
