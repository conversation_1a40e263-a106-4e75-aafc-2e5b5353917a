<?php

class ApiController extends BaseController
{

    private $service;
    private $orders;
    private $orderDetails;
    private $users;
    private $rates;
    private $orderService;


    public function __construct()
    {
        $this->service = new ApiService();
        $this->orders = new CrudModel('orders');
        $this->orderDetails = new CrudModel('order_details');
        $this->users = new CrudModel('users');
        $this->rates =  new CrudModel('delivery_rates');
        $this->orderService = new OrderService();
        

    }

    public function placeOrder() {
        try {

            $this->orders->beginTransaction();

            $rawInput = file_get_contents('php://input');
            $input = json_decode($rawInput, true);
            
            if (json_last_error() !== JSON_ERROR_NONE) {
                $this->logError('Error Invalid JSON input');

                http_response_code(400);
                echo json_encode(['error' => 'Invalid JSON input: ' . json_last_error_msg()]);
                return;
            }

            if (!$input) {
                $this->logError("Invalid input: $rawInput");

                http_response_code(400);
                echo json_encode(['error' => 'Invalid JSON input.']);
                return;
            }
            
            if (!isset($input['customer'])) {
                $this->logError('Missing "form" data.');

                http_response_code(400);
                echo json_encode(['error' => 'Missing "form" data.']);
                return;
            }
            
            if (!isset($input['products'])) {
                $this->logError('Missing "cart" data.');

                http_response_code(400);
                echo json_encode(['error' => 'Missing "cart" data.']);
                return;
            }
            
            $requiredFields = ['name', 'phone', 'address'];
            foreach ($requiredFields as $field) {
                if (empty($input['customer'][$field])) {
                    $this->logError('Missing required field: '. $field);

                    http_response_code(400);
                    echo json_encode(['error' => "Missing required field: $field"]);
                    return;
                }
            }
        
            $name = $input['customer']['name'];
            $phone = $input['customer']['phone'];
            $password = $input['customer']['password'] ?? ''; // Optional field
            $state = $input['customer']['state'] ?? '';       // Optional field
            $city = $input['customer']['city'] ?? '';         // Optional field
            $address = $input['customer']['address'];
            $note = $input['customer']['note'] ?? '';
            $cart = $input['products'];
            $shippingFee = $input['shippingFee'] ?? 0; // Default to 0 if not provided
            $subtotal = $input['total'] ?? 0;      // Default to 0 if not provided
        
            $hashedPassword = $password ? password_hash($password, PASSWORD_DEFAULT) : null;
        
            $userData = [
                'password' => $hashedPassword,
                'role' => 'customer',
                'name' => $name,
                'username' => $name,

                'phone' => $phone,
                'country' => 'العراق',
                'state' => $state,
                'city' => $city,
                'address' => $address,
            ];
        
            $userId = $this->users->createElementGetId($userData);
            if (!$userId) {
                $this->logError('Failed to save user information.');
                $this->orders->rollBack();

                http_response_code(500);
                echo json_encode(['error' => 'Failed to save user information.']);
                return;
            }
            $total_amount = 0;

            foreach ($cart as $productId => $product) {
                $price = isset($product['price']) ? (float) $product['price'] : 0;
                $quantity = isset($product['quantity']) ? (int) $product['quantity'] : 0;
                $total_amount += $price * $quantity;
            }
        
            $orderData = [
                'customer_id' => $userId,
                'total_amount' => $total_amount,
                'delivery_fee' => $shippingFee
            ];
        
            $orderId = $this->orders->createElementGetId($orderData);
            if (!$orderId) {
                $this->logError('Failed to place the order.');
                $this->orders->rollBack();

                http_response_code(500);
                echo json_encode(['error' => 'Failed to place the order.']);
                return;
            } else {
                foreach ($cart as $product) {

                    $name = $product['name'];
                    $price = $product['price'];
                    $quantity = $product['quantity'];
                    $total_price = $quantity * $price;
    
                    $orderDetails = [
                        'order_id' => $orderId,
                        'product_id' => $product['id'],
                        'store_id' => 0,
                        'quantity' => $quantity,
                        'price' => $price,
                        'total_price' => $total_price
                    ];

                    $this->logError(json_encode($orderDetails));

                    if (!$this->orderDetails->createElement($orderDetails)) {
                        $this->logError('Failed to place the order.');
                        $this->orders->rollBack();
        
                        http_response_code(500);
                        echo json_encode(['error' => 'Failed to place the order.']);
                        return;
                    }
                
                }
            }

            $this->orders->commit();

            $user = $this->users->getElement($userId);
            $token = $this->generateJwt($user['id'], $user['role']);
            http_response_code(200);


            echo json_encode([
                'jwt' => $token,
                'user' => [
                    'id' => $user['id'],
                    'name' => $user['name'],

                    'username' => $user['username'],

                    'phone' => $user['phone'],
                    'state' => $user['state'],
                    'city' => $user['city'],
                    'address' => $user['address'],
                    'created_at' => $user['created_at'],
                ]
            ]);
            $this->logError(json_encode([
                'jwt' => $token,
                'user' => [
                    'id' => $user['id'],
                    'name' => $user['name'],

                    'username' => $user['username'],

                    'phone' => $user['phone'],
                    'state' => $user['state'],
                    'city' => $user['city'],
                    'address' => $user['address'],
                    'created_at' => $user['created_at'],
                ]
            ]));

            //echo json_encode(['message' => 'Order placed successfully.', 'orderId' => $orderId]);

        } catch (Exception $e) {
            $this->logError($e->getMessage());
            $this->orders->rollBack();
            return $this->redirect('/error');
        }
    }

    public function fee()
    {

        $rates = $this->rates->getElements();
        $fee = $this->getOurFee();

        $data = [
            'rates' => $rates,
            'fee' => $fee
        ];

        $this->json($data);
    }

    function getOurFee() {
        $filePath = '../config/settings.json';
        if (!file_exists($filePath)) {
            die("Error: File not found.");
        }
        $fileContent = file_get_contents($filePath);
        $data = json_decode($fileContent, true);

        if (json_last_error() !== JSON_ERROR_NONE) {
            die("Error: Invalid JSON format.");
        }
        return $data['ourFee'] ?? null;
    }

    public function products()
    {
        $searchQuery = isset($_GET['search']) ? htmlspecialchars(trim($_GET['search'])) : '';

        $filters = [];

        if (!empty($_GET['category_id'])) {
            $filters['category_id'] = (int) $_GET['category_id']; 
        }

        if (!empty($_GET['store_id'])) {
            $filters['store_id'] = (int) $_GET['store_id'];
        }

        if (!empty($searchQuery)) {
            $filters['name'] = ['operator' => 'like', 'value' => $searchQuery];
        }

        if (!empty($_GET['min_price']) && !empty($_GET['max_price'])) {
            $filters['price'] = ['operator' => 'between', 'value' => [(float) $_GET['min_price'], (float) $_GET['max_price']]];
        }

        $page = isset($_GET['page']) ? max(1, (int)$_GET['page']) : 1;
        $limit = PHP_INT_MAX;
        $pagination = ['page' => $page, 'limit' => $limit];

        $sortOption = $_GET['sort'] ?? 'price_asc';
        switch ($sortOption) {
            case 'price_asc':
                $sorting = ['column' => 'price', 'order' => 'ASC'];
                break;
            case 'price_desc':
                $sorting = ['column' => 'price', 'order' => 'DESC'];
                break;
            case 'name_asc':
                $sorting = ['column' => 'name', 'order' => 'ASC'];
                break;
            case 'name_desc':
                $sorting = ['column' => 'name', 'order' => 'DESC'];
                break;
            default:
                $sorting = ['column' => 'price', 'order' => 'ASC'];
        }

        $products = $this->service->getFilteredProducts($filters, $pagination, $sorting);

        $this->json($products);
    }

    public function productsOld()
    {

        $products = $this->service->getProducts();

        $this->json($products);
    }

    public function slides()
    {
        $slides = $this->service->getSlides();
        $this->json($slides);
    }

    public function shops()
    {
        $shops = $this->service->getShops();
        $this->json($shops);
    }

    public function categories()
    {
        $categories = $this->service->getCategories();
        $this->json($categories);
    }

    public function orders()
    {
        $orders = $this->orderService->getOrders();
        $this->json($orders);
    }
}
