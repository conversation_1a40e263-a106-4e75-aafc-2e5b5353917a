<?php

class AccountingController extends BaseController
{

    private $model;

    public function __construct($request, $response)
    {
        parent::__construct($request, $response);
        $this->model = new DashboardModel();
    }

    public function index()
    {
        if ($this->isLoggedIn()) {
            $userId = $this->getSessionData('user_id');
            $user = $this->model->getUserData($userId);
            $data = [
                'title' => 'Dashboard',
                'style' => null,
                'user' => $user,
            ];

            $this->render('../app/view/admin/dashboard.php', $data);
        }else {
            $this->redirect('/login');
        }
    }

    public function reports()
    {
        if ($this->isLoggedIn()) {
            $userId = $this->getSessionData('user_id');
            $user = $this->model->getUserData($userId);
            $data = [
                'title' => 'Dashboard',
                'style' => null,
                'user' => $user,
            ];

            $this->render('../app/view/admin/reports.php', $data);
        }else {
            $this->redirect('/login');
        }
    }

    public function settings()
    {
        if ($this->isLoggedIn()) {
            $userId = $this->getSessionData('user_id');
            $user = $this->model->getUserData($userId);
            $data = [
                'title' => 'Dashboard',
                'style' => null,
                'user' => $user,
            ];

            $this->render('../app/view/admin/settings.php', $data);
        }else {
            $this->redirect('/login');
        }
    }
}
