-- إعداد إضافي لقاعدة البيانات
USE wardena_db;

-- تحسين إعدادات MySQL
SET GLOBAL innodb_buffer_pool_size = 134217728; -- 128MB
SET GLOBAL query_cache_size = 33554432; -- 32MB
SET GLOBAL max_connections = 100;

-- إنشاء مستخدم إضافي للقراءة فقط (اختياري)
CREATE USER IF NOT EXISTS 'wardena_read'@'%' IDENTIFIED BY 'read123';
GRANT SELECT ON wardena_db.* TO 'wardena_read'@'%';

-- تحسين الجداول
OPTIMIZE TABLE users;
OPTIMIZE TABLE stores;
OPTIMIZE TABLE products;
OPTIMIZE TABLE orders;

-- إضافة بيانات تجريبية (اختياري)
INSERT IGNORE INTO admin_settings (setting_key, setting_value, setting_type, description) VALUES
('app_maintenance_mode', 'false', 'boolean', 'تفعيل وضع الصيانة'),
('app_registration_enabled', 'true', 'boolean', 'السماح بالتسجيل الجديد'),
('app_max_upload_size', '10485760', 'number', 'الحد الأقصى لحجم الملف بالبايت'),
('app_default_language', 'ar', 'string', 'اللغة الافتراضية'),
('app_timezone', 'Asia/Baghdad', 'string', 'المنطقة الزمنية');

-- إنشاء فئات منتجات تجريبية
INSERT IGNORE INTO product_categories (id, name, description, is_active) VALUES
(1, 'إلكترونيات', 'أجهزة إلكترونية وتقنية', 1),
(2, 'ملابس', 'ملابس رجالية ونسائية', 1),
(3, 'طعام ومشروبات', 'مواد غذائية ومشروبات', 1),
(4, 'كتب', 'كتب ومجلات', 1),
(5, 'رياضة', 'معدات رياضية', 1);

-- إنشاء علامات تجارية تجريبية
INSERT IGNORE INTO brands (id, name, description, is_active) VALUES
(1, 'سامسونج', 'علامة تجارية للإلكترونيات', 1),
(2, 'آبل', 'علامة تجارية للتقنية', 1),
(3, 'نايكي', 'علامة تجارية رياضية', 1),
(4, 'أديداس', 'علامة تجارية رياضية', 1);

-- إضافة إحصائيات أولية
INSERT IGNORE INTO visits (ip_address, user_agent, visit_time, page_visited, referrer) VALUES
('127.0.0.1', 'Docker Setup', NOW(), '/', 'direct');

FLUSH PRIVILEGES;
