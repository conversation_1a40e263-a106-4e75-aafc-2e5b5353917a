<?php

class PasswordModel
{

    private $db;
    private $mail;

    public function __construct()
    {
        $this->db = Database::getInstance()->connect();
        $this->mail = new PHPMailer(true);
    }
    
    // Satrt Password Reset
    public function getUserByEmail($email)
    {
        $stmt = $this->db->prepare("SELECT * FROM users WHERE email = :email");
        $stmt->bindParam(':email', $email);

        $stmt->execute();

        return $stmt->fetch(PDO::FETCH_ASSOC);
    }
    
    public function storePasswordResetToken($userId, $token, $expiresAt) {
        $stmt = $this->db->prepare("INSERT INTO password_reset_tokens (user_id, token, expires_at) VALUES (:user_id, :token, :expires_at)");
        $stmt->bindParam(':user_id', $userId);
        $stmt->bindParam(':token', $token);
        $stmt->bindParam(':expires_at', $expiresAt);
        $stmt->execute();
    }

    public function getUserByPasswordResetToken($token){

        $stmt = $this->db->prepare("SELECT * FROM password_reset_tokens WHERE token = :token");
        $stmt->bindParam(':token', $token);

        $stmt->execute();

        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    function sendPasswordResetEmail($userEmail, $token) {
        $mailer = $this->mail;
        $resetLink = "https://pearlfibers.net/password/reset/token=$token";
        
        try {

            $mailer->isSMTP();
            $mailer->Host = 'mail.pearlfibers.net';  // Specify main and backup SMTP servers
            $mailer->SMTPAuth = true;
            $mailer->Username = '<EMAIL>';  // SMTP username
            $mailer->Password = 'Admin-00!';  // SMTP password
            $mailer->SMTPSecure = 'tls';  // Enable TLS encryption, `ssl` also accepted
            $mailer->Port = 587;  // TCP port to connect to
    
            //Recipients
            $mailer->setFrom('<EMAIL>', 'PearlFibers Password Reset');
            $mailer->addAddress($userEmail);     // Add a recipient
    
            // Content
            $mailer->isHTML(true);  // Set email format to HTML
            $mailer->Subject = 'Reset Your Password';
            $mailer->Body    = "Please click on the following link to reset your password: <a href='{$resetLink}'>Reset Password</a>";
            $mailer->AltBody = "Please copy and paste the following link into your browser to reset your password: {$resetLink}";
    
            $mailer->send();
            return ['success' => true, 'errors' => "Message has been sent"];
        } catch (Exception $e) {
            return ['success' => false, 'errors' => "Message could not be sent. Mailer Error: {$mailer->ErrorInfo}"];
        }
    }

    function sendPasswordResetSuccessEmail($userEmail) {
        $mailer = $this->mail;
        $Link = "https://pearlfibers.net/login";
        
        try {

            $mailer->isSMTP();
            $mailer->Host = 'mail.pearlfibers.net';  // Specify main and backup SMTP servers
            $mailer->SMTPAuth = true;
            $mailer->Username = '<EMAIL>';  // SMTP username
            $mailer->Password = 'Admin-00!';  // SMTP password
            $mailer->SMTPSecure = 'tls';  // Enable TLS encryption, `ssl` also accepted
            $mailer->Port = 587;  // TCP port to connect to
    
            //Recipients
            $mailer->setFrom('<EMAIL>', 'PearlFibers Password Reset');
            $mailer->addAddress($userEmail);     // Add a recipient
    
            // Content
            $mailer->isHTML(true);  // Set email format to HTML
            $mailer->Subject = 'Password Resest Success';
            $mailer->Body    = "You've successfuly changed your password: <a href='{$Link}'>Login</a>";
            $mailer->AltBody = "Please ...";
    
            $mailer->send();
            return ['success' => true, 'msg' => "You've successfuly changed your password"];
        } catch (Exception $e) {
            return ['success' => false, 'msg' => "Check for the new password. Message could not be sent. Mailer Error: {$mailer->ErrorInfo}"];
        }
    }

    public function resetPassword($token, $newPassword) {
        $user = $this->getUserByPasswordResetToken($token);
        // Update the user's password and invalidate the token
        $hashedPassword = password_hash($newPassword, PASSWORD_DEFAULT);    
        $sent = $this->updatePasswordAndInvalidateToken($user['user_id'], $hashedPassword);
        if ($sent['success'] == true) {
            return ['success' => true, 'errors' => $sent['errors']];
        }
        elseif ($sent['success'] == false) {
            return ['success' => false, 'errors' => $sent['errors']];
        }
    }
    public function getUserEmail($userId){
        $stmt = $this->db->prepare("SELECT * FROM users WHERE id = :userId");
        $stmt->bindParam(':userId', $userId);
        $stmt->execute();
        $row = $stmt->fetch(PDO::FETCH_ASSOC);
        return $row['email'];
    }
    
    private function updatePasswordAndInvalidateToken($userId, $hashedPassword) {

        $stmt = $this->db->prepare("UPDATE users SET password = :password WHERE id = :userId");
        $stmt->bindParam(':password', $hashedPassword);
        $stmt->bindParam(':userId', $userId);
        $stmt->execute();
        $stmt = null;

        $stmtToken = $this->db->prepare("DELETE FROM password_reset_tokens WHERE user_id = :userId");
        $stmtToken->bindParam(':userId', $userId);
        $stmtToken->execute();
        $stmtToken = null;

        $userEmail = $this->getUserEmail($userId);
        $sent = $this->sendPasswordResetSuccessEmail($userEmail);
        if ($sent['success'] == true) {
            return ['success' => true, 'errors' => $sent['msg']];
        }
        elseif ($sent['success'] == false) {
            return ['success' => false, 'errors' => $sent['msg']];
        }
    }

    public function validatePassword($data) {
        $user = $this->getUserByPasswordResetToken($data['token']);
    
        $stmt = $this->db->prepare("SELECT password FROM users WHERE id = :userId");
        $stmt->bindParam(':userId', $user['user_id']);
        $stmt->execute();
        $row = $stmt->fetch(PDO::FETCH_ASSOC);
    
        if (!password_verify($data['password'], $row['password'])) {
            return false;
        }
    
        return true;
    }
}
