#!/bin/bash

# ===================================
# Wardena API - Docker Startup Script
# ===================================

echo "🚀 Starting Wardena API with Docker..."
echo "======================================"

# التحقق من وجود Docker
if ! command -v docker &> /dev/null; then
    echo "❌ Docker is not installed. Please install Docker first."
    echo "   Download from: https://www.docker.com/products/docker-desktop"
    exit 1
fi

# التحقق من وجود Docker Compose
if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose is not installed. Please install Docker Compose first."
    exit 1
fi

# إنشاء المجلدات المطلوبة
echo "📁 Creating required directories..."
mkdir -p storage/cache
mkdir -p storage/logs
mkdir -p public/uploads
mkdir -p docker/apache
mkdir -p docker/php
mkdir -p docker/mysql

# إعداد الصلاحيات
echo "🔐 Setting permissions..."
chmod -R 755 storage/
chmod -R 755 public/uploads/
chmod +x start.sh

# نسخ ملف البيئة للـ Docker
echo "⚙️ Setting up environment..."
if [ ! -f .env ]; then
    cp .env.docker .env
    echo "✅ Created .env file from .env.docker"
else
    echo "ℹ️ .env file already exists"
fi

# إيقاف الحاويات السابقة (إن وجدت)
echo "🛑 Stopping existing containers..."
docker-compose down --remove-orphans

# بناء وتشغيل الحاويات
echo "🔨 Building and starting containers..."
docker-compose up --build -d

# انتظار تشغيل قاعدة البيانات
echo "⏳ Waiting for database to be ready..."
sleep 30

# التحقق من حالة الحاويات
echo "📊 Checking container status..."
docker-compose ps

# عرض معلومات الوصول
echo ""
echo "🎉 Wardena API is now running!"
echo "================================"
echo "🌐 Main Application: http://localhost:8080"
echo "🔧 API Health Check: http://localhost:8080/api/health"
echo "🗄️ phpMyAdmin: http://localhost:8081"
echo "📊 Quick Setup: http://localhost:8080/quick-setup.php"
echo "🧪 System Test: http://localhost:8080/test-setup.php"
echo ""
echo "📋 Database Information:"
echo "   Host: localhost:3306"
echo "   Database: wardena_db"
echo "   Username: wardena"
echo "   Password: wardena123"
echo ""
echo "👤 Default Admin Login:"
echo "   Email: <EMAIL>"
echo "   Password: password"
echo ""
echo "🛑 To stop: docker-compose down"
echo "📝 To view logs: docker-compose logs -f"
echo "🔄 To restart: ./start.sh"

# فتح المتصفح تلقائياً (اختياري)
if command -v xdg-open &> /dev/null; then
    echo "🌐 Opening browser..."
    xdg-open http://localhost:8080
elif command -v open &> /dev/null; then
    echo "🌐 Opening browser..."
    open http://localhost:8080
elif command -v start &> /dev/null; then
    echo "🌐 Opening browser..."
    start http://localhost:8080
fi

echo ""
echo "✅ Setup completed successfully!"
