<?php

class DeliveryController extends BaseController
{

    private $orders;
    private $model;

    private $users;
    private $val;
    private $auth;
    private $orderService;



    public function __construct($request, $response)
    {
        parent::__construct($request, $response);
        $this->orders = new CrudModel('orders');
        $this->model = new CrudModel('delivery_rates');

        $this->users = new CrudModel('users');
        $this->val = new ValidateModel('delivery_rates');
        $this->auth = new AuthService();
        $this->orderService = new OrderService();


    }

    public function index()
    {
        if ($this->isLoggedIn()) {
            $userId = $this->getSessionData('user_id');
            $user = $this->users->getElement($userId);
            $result = $this->orders->getElementsWhere('delivery_boy_id', $userId);

            $ordersCount = count($result);

            $waiting = 0;
            foreach ($result as $order) {
                if(!$order['status'] == 'delivered' || !$order['status'] == 'canceled' ){
                    $waiting++;
                }
            }

            $count = [
                'waiting_orders' => $waiting,
                'orders' => $ordersCount,
            ];

            $data = [
                'title' => 'Delivery Dashboard',
                'style' => null,
                'user' => $user,
                'result' => $result,
                'count' => $count
            ];
            $this->render('../app/view/delivery/dashboard.php', $data);
        } else {
            $this->redirect('/login');
        }
    }

    public function orders()
    {
        if ($this->auth->delivery() == true) {
            $userId = $this->getSessionData('user_id');
            $user = $this->users->getElement($userId);
            $result = $this->orderService->getDeliveryOrders($userId);
            $data = [
                'title' => 'Store Admin Dashboard',
                'style' => null,
                'user' => $user,
                'result' => $result,
            ];
            $this->render('../app/view/delivery/orders.php', $data);
        } else {
            $this->redirect('/login');
        }
    }


    public function getOrder()
    {
        if ($this->auth->owner() == true) {

            $userId = $this->getSessionData('user_id');
            $user = $this->users->getElement($userId);
            $store = $this->model->getElementsWhere('owner_id', $userId);
            $store_id = $store[0]['id'];

            $id = $_GET['id'];
            $result = $this->orderService->getStoreOrder($id, $store_id);

            http_response_code(200);
            echo json_encode(['message' => 'Order placed successfully.', 'data' => $result]);
        } else {
            $this->redirect('/login');
        }
    }

    public function rates()
    {
        if ($this->auth->admin() == true) {
            
                $userId = $this->getSessionData('user_id');
                $user = $this->users->getElement($userId);
                $result = $this->model->getElements();
                $data = [
                    'title' => 'Delivery Rates',
                    'style' => null,
                    'user' => $user,
                    'result' => $result,
                ];
                $this->render('../app/view/admin/delivery-rates.php', $data);

        } else {
            echo "error";
        }
    }

    
    public function new()
    {
        if ($this->isLoggedIn()) {
            if ($this->isPost()) {
                $this->handleNew();
            } else {
                $userId = $this->getSessionData('user_id');
                $user = $this->model->getElement($userId);
                $result = $this->model->getElements();
                $data = [
                    'title' => 'Delivery Rates',
                    'style' => null,
                    'user' => $user,
                    'result' => $result,
                ];
                $this->render('../app/view/admin/delivery_rates.php', $data);
            }
        } else {
            echo "error";
        }
    }


    private function handleNew()
    {

        $fields = ['city', 'fee'];
        $formData = $this->getFormData($fields);
        $validationErrors = $this->validateFormData($formData);

        if (!empty($validationErrors)) {
            $data = [
                'errors' => $validationErrors,
                'old' => $formData,
            ];
            $this->redirect('/settings/rates', $data);
            $this->logError('Failed to validate rate form data');
            return;
        }

        if ($this->model->createElement($formData)) {
            $this->redirect('/settings/rates');
        } else {
            $this->setAlert('There was a problem during registration. Please try again!', 'error');
        }

    }

    public function update()
    {
        try {
            $this->model->beginTransaction();

            $id = $_GET['id'];

            $fields = ['city', 'fee'];
            $formData = $this->getFormData($fields);

            if (!$this->model->updateElement($formData, $id)) {
                $this->model->rollBack();
                return false;
            } else {
                $this->model->commit();
                $this->redirect('/settings/rates');
                return;
            }
        } catch (Exception $e) {
            $this->model->rollBack();
            $this->logError($e->getMessage());
            return $this->redirect('/error');
        }
    }

    public function delete()
    {
        try {
            $this->model->beginTransaction();

            $id = $_GET['id'];

            if (!$this->model->delete($id)) {
                $this->model->rollBack();
                return false;
            } else {
                $this->model->commit();
                $this->redirect('/delivery/rates');
                return;
            }
        } catch (Exception $e) {
            $this->model->rollBack();
            $this->logError($e->getMessage());
            return $this->redirect('/error');
        }
    }

    private function validateFormData($data)
    {
        $errors = [];
        $requiredFields = ['city', 'fee'];

        foreach ($requiredFields as $field) {
            if (empty($data[$field])) {
                $errors[$field] = $this->formatFieldName($field) . ' is required!';
            }
        }

        if ($this->val->elementExists('city', $data['city'])) {
            $errors['city'] = 'this city is already exist.';
        }

        return $errors;
    }

}
