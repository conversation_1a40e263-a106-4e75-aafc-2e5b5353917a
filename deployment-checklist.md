# قائمة مراجعة النشر - Wardena API

## قبل النشر 📋

### 1. التحقق من الملفات المحلية
- [ ] اختبار النظام محلياً
- [ ] تشغيل `php test-setup.php` للتأكد من سلامة الإعداد
- [ ] التأكد من عمل جميع API endpoints
- [ ] مراجعة ملفات السجلات للأخطاء

### 2. إعداد الاستضافة
- [ ] شراء استضافة تدعم PHP 7.4+ و MySQL 5.7+
- [ ] الحصول على شهادة SSL
- [ ] إعداد النطاق (Domain)
- [ ] الوصول إلى لوحة التحكم (cPanel/Plesk)

### 3. إعداد قاعدة البيانات
- [ ] إنشاء قاعدة بيانات MySQL جديدة
- [ ] إنشاء مستخدم قاعدة بيانات
- [ ] منح جميع الصلاحيات للمستخدم
- [ ] تسجيل معلومات الاتصال (Host, Database, User, Password)

## أثناء النشر 🚀

### 1. رفع الملفات
```bash
# ضغط الملفات (اختياري)
tar -czf wardena-api.tar.gz --exclude='.git' --exclude='node_modules' .

# رفع عبر FTP/SFTP أو File Manager
# تأكد من رفع جميع الملفات والمجلدات
```

**الملفات المطلوبة:**
- [ ] `app/` - ملفات التطبيق
- [ ] `config/` - ملفات الإعداد
- [ ] `core/` - الملفات الأساسية
- [ ] `public/` - الملفات العامة
- [ ] `storage/` - مجلد التخزين
- [ ] `vendor/` - المكتبات الخارجية
- [ ] `.env` - متغيرات البيئة
- [ ] `.htaccess` - إعدادات Apache

### 2. إعداد المجلدات
```bash
# إعداد الصلاحيات
chmod 755 public/
chmod 755 public/uploads/
chmod 755 storage/
chmod 644 .env
chmod 644 config/
```

### 3. تحديث ملف .env
```env
# تحديث للإنتاج
APP_ENV=production
URL=https://yourdomain.com/
API_URL=https://yourdomain.com/api/

# معلومات قاعدة البيانات
DB_HOST=your-database-host
DB_NAME=your_database_name
DB_USER=your_database_user
DB_PASS=your_secure_password

# مفاتيح الأمان (إنشاء مفاتيح جديدة)
SECRET=your-new-secret-key
JWT_SECRET=your-new-jwt-secret
```

### 4. إعداد قاعدة البيانات
```bash
# الطريقة الأولى: عبر المتصفح
https://yourdomain.com/config/database-setup.php

# الطريقة الثانية: عبر phpMyAdmin
# استيراد ملف config/general.sql

# الطريقة الثالثة: عبر SSH
mysql -u username -p database_name < config/general.sql
```

### 5. اختبار النشر
- [ ] زيارة الموقع الرئيسي
- [ ] اختبار API: `GET /api/health`
- [ ] اختبار تسجيل الدخول
- [ ] اختبار رفع الملفات
- [ ] مراجعة ملفات السجلات

## بعد النشر ✅

### 1. الأمان
- [ ] تغيير كلمة مرور الأدمن الافتراضية
- [ ] تحديث مفاتيح الأمان في .env
- [ ] تفعيل HTTPS وإعادة التوجيه
- [ ] حذف ملفات الإعداد الحساسة:
  ```bash
  rm quick-setup.php
  rm test-setup.php
  rm deployment-checklist.md
  ```

### 2. التحسين
- [ ] تفعيل ضغط gzip
- [ ] إعداد التخزين المؤقت
- [ ] تحسين إعدادات PHP
- [ ] إعداد CDN (اختياري)

### 3. المراقبة
- [ ] إعداد مراقبة الموقع (Uptime monitoring)
- [ ] إعداد تنبيهات الأخطاء
- [ ] مراجعة ملفات السجلات يومياً
- [ ] إعداد النسخ الاحتياطي التلقائي

### 4. النسخ الاحتياطي
```bash
# نسخ احتياطي لقاعدة البيانات (يومي)
mysqldump -u username -p database_name > backup_$(date +%Y%m%d).sql

# نسخ احتياطي للملفات (أسبوعي)
tar -czf files_backup_$(date +%Y%m%d).tar.gz public/uploads/

# إعداد cron job للنسخ الاحتياطي التلقائي
# 0 2 * * * /path/to/backup-script.sh
```

## استكشاف الأخطاء 🔧

### مشاكل شائعة:

**1. خطأ 500 Internal Server Error**
- تحقق من ملف error.log
- تأكد من صلاحيات الملفات
- تحقق من إعدادات PHP

**2. خطأ في قاعدة البيانات**
- تحقق من معلومات الاتصال في .env
- تأكد من وجود قاعدة البيانات
- تحقق من صلاحيات المستخدم

**3. مشاكل في الـ API**
- تحقق من إعدادات .htaccess
- تأكد من تفعيل mod_rewrite
- تحقق من إعدادات CORS

**4. مشاكل في رفع الملفات**
- تحقق من صلاحيات مجلد uploads
- تحقق من حدود رفع الملفات في PHP
- تأكد من وجود مساحة كافية

### ملفات السجلات:
- `error.log` - أخطاء التطبيق
- `/var/log/apache2/error.log` - أخطاء Apache
- `/var/log/mysql/error.log` - أخطاء MySQL

## إعدادات الأداء 🚄

### PHP Configuration (php.ini):
```ini
memory_limit = 256M
upload_max_filesize = 10M
post_max_size = 10M
max_execution_time = 300
max_input_vars = 3000
```

### MySQL Configuration:
```sql
-- تحسين الأداء
SET GLOBAL innodb_buffer_pool_size = 128M;
SET GLOBAL query_cache_size = 32M;
SET GLOBAL max_connections = 100;
```

### Apache Configuration:
```apache
# تفعيل الضغط
LoadModule deflate_module modules/mod_deflate.so

# تفعيل التخزين المؤقت
LoadModule expires_module modules/mod_expires.so
```

## الصيانة الدورية 🔄

### يومياً:
- [ ] مراجعة ملفات السجلات
- [ ] مراقبة استخدام الموارد
- [ ] التحقق من عمل النسخ الاحتياطي

### أسبوعياً:
- [ ] تحديث النظام والمكتبات
- [ ] تنظيف ملفات السجلات القديمة
- [ ] مراجعة الأمان

### شهرياً:
- [ ] مراجعة الأداء والإحصائيات
- [ ] تحديث كلمات المرور
- [ ] اختبار استعادة النسخ الاحتياطي

## جهات الاتصال 📞

**الدعم الفني:**
- البريد الإلكتروني: <EMAIL>
- الوثائق: https://docs.wardena.app
- GitHub: https://github.com/your-repo/wardena-api

**المطور:**
- Pearl Fibers: https://pearlfibers.com/

---

**✅ تم إكمال النشر بنجاح!**

تذكر: الأمان أولاً، والنسخ الاحتياطي ثانياً، والأداء ثالثاً.
