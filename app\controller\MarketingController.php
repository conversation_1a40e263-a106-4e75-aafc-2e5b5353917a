<?php

class MarketingController extends BaseController
{

    private $model;
    private $slide;
    private $products;
    private $shops;

    private $service;
    private $categories;
    private $users;
    private $auth;


    public function __construct($request, $response)
    {
        parent::__construct($request, $response);
        $this->model = new CrudModel('products');
        $this->categories = new CrudModel('product_categories');
        $this->service = new ProductService();

        $this->slide = new CrudModel('slides');
        $this->products = new CrudModel('products');
        $this->shops = new CrudModel('stores');

        $this->users = new CrudModel('users');
        $this->auth = new AuthService();

    }

    public function index()
    {
        
        if ($this->isLoggedIn()) {
            $userId = $this->getSessionData('user_id');
            $user = $this->users->getElement($userId);
            $data = [
                'title' => 'التسويق',
                'style' => null,
                'user' => $user
            ];
            $this->render('../app/view/admin/marketing.php', $data);
        } else {
            $this->redirect('/login');
        }
    }

    public function shops()
    {
        
        if ($this->isLoggedIn()) {
            $userId = $this->getSessionData('user_id');
            $user = $this->users->getElement($userId);
            $filters = [];
            $pagination = ['page' => 1, 'limit' => 25];
            $sorting = ['column' => 'ordering', 'order' => 'ASC'];

            $shops = $this->shops->getFiltered($filters, $pagination, $sorting);
            $result = $shops['data'];

            $data = [
                'title' => 'تسويق المتاجر',
                'style' => null,
                'user' => $user,
                'shops' => $result
            ];
            $this->render('../app/view/admin/marketing-shops.php', $data);
        } else {
            $this->redirect('/login');
        }
    }

    public function updateShopsOrder()
    {
        $data = json_decode(file_get_contents("php://input"), true);
    
        if (!empty($data)) {
            try {
                $this->shops->beginTransaction();
    
                foreach ($data as $shop) {
                    $id = (int) $shop['id'];
                    $formData['ordering'] = (int) $shop['order'];
    
                    if (!$this->shops->updateElement($formData, $id)) {
                        throw new Exception("Failed to update shop ID $id");
                    }
                }
    
                $this->shops->commit();
                echo json_encode(['status' => 'success']);
                return;
    
            } catch (Exception $e) {
                $this->shops->rollBack();
                $this->logError($e->getMessage());
                echo json_encode(['status' => 'error', 'message' => $e->getMessage()]);
                return;
            }
        }
    
        echo json_encode(['status' => 'error', 'message' => 'Invalid data']);
    }
    

    public function bestSeller()
    {
        if ($this->auth->admin() == true) {
            
            $userId = $this->getSessionData('user_id');
            $user = $this->users->getElement($userId);
            $searchQuery = isset($_GET['search']) ? htmlspecialchars(trim($_GET['search'])) : '';
            $filters = [];
            if (!empty($_GET['category_id'])) {
                $filters['category_id'] = (int) $_GET['category_id']; 
            }
            if (!empty($searchQuery)) {
                $filters['name'] = ['operator' => 'like', 'value' => $searchQuery];
            }
            if (!empty($_GET['min_price']) && !empty($_GET['max_price'])) {
                $filters['price'] = ['operator' => 'between', 'value' => [(float) $_GET['min_price'], (float) $_GET['max_price']]];
            }
            $page = isset($_GET['page']) ? max(1, (int)$_GET['page']) : 1;
            $limit = 25;
            $pagination = ['page' => $page, 'limit' => $limit];
            $sortOption = $_GET['sort'] ?? 'price_asc';
            switch ($sortOption) {
                case 'price_asc':
                    $sorting = ['column' => 'id', 'order' => 'DESC'];
                    break;
                case 'price_desc':
                    $sorting = ['column' => 'price', 'order' => 'DESC'];
                    break;
                case 'name_asc':
                    $sorting = ['column' => 'name', 'order' => 'ASC'];
                    break;
                case 'name_desc':
                    $sorting = ['column' => 'name', 'order' => 'DESC'];
                    break;
                default:
                    $sorting = ['column' => 'price', 'order' => 'ASC'];
            }

            $filtered = $this->service->getFilteredProducts($filters, $pagination, $sorting);
            $result = $filtered['products'];
            $total = $filtered['total'];
            $totalPages = ceil($total / $pagination['limit']);

            $bestFilters['best_seller'] = ['operator' => '=', 'value' => '1'];
            $bestSeller = $this->service->getFilteredProducts($bestFilters, $pagination, $sorting);
            $best = $bestSeller['products'];

            $topFilters['top_products'] = ['operator' => '=', 'value' => '1'];
            $topSeller = $this->service->getFilteredProducts($topFilters, $pagination, $sorting);
            $top = $topSeller['products'];

            $categories = $this->categories->getElements();
            $hierarchicalCategories = $this->buildHierarchy($categories);
            $flatCategories = $this->flattenHierarchy($hierarchicalCategories);

                $data = [
                    'title' => 'تسويق المنتجات',
                    'style' => null,
                    'user' => $user,
                    'result' => $result,
                    'totalPages' => $totalPages,
                    'categories' => $flatCategories,
                    'currentPage' => $pagination['page'],
                    'searchQuery' => $searchQuery,
                    'selectedSort' => $sortOption,
                    'best' => $best,
                    'top' => $top,

                ];
                $this->render('../app/view/admin/marketing-best-seller.php', $data);

        } else {
            echo "error";
        }
    }

    public function update()
    {
        try {

            $this->model->beginTransaction();

            $id = $_GET['id'];
            $type = $_GET['type'];
            $v = $_GET['value'];

            $formData[$type] = $v;

            if (!$this->model->updateElement($formData, $id)) {
                $this->logError('Failed to create store');
                $this->model->rollBack();
            } else {
                $this->model->commit();
                $with = ['response' => 'success'];
                $this->redirect('/marketing/best-seller', $with);
                return;
            }
        } catch (Exception $e) {
            $this->model->rollBack();
            $this->logError($e->getMessage());
            return $this->redirect('/marketing/best-sellers');
        }
    }

}
