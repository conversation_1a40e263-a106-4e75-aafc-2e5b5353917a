<?php

class StoreService
{
    private $users;
    private $stores;


    public function __construct()
    {
        $this->users = new CrudModel('users');
        $this->stores = new CrudModel('stores');

    }

    public function createStore($data)
    {
        return $this->stores->createElement($data);
    }

    public function getOrdered(){
        $filters = [];
        $pagination = ['page' => 1, 'limit' => 25];
        $sorting = ['column' => 'ordering', 'order' => 'ASC'];
        $shops = $this->stores->getFiltered($filters, $pagination, $sorting);
        $stores = $shops['data'];
        $data = [];
        foreach ($stores as $store) {
            $user = $this->users->getElement($store['owner_id']);
            $data[] = [
                'id' => $store['id'],
                'store' => $store['name'],
                'img' => $store['img'],
                'name' => $user['name'],
                'last_name' => $user['last_name'],
                'email' => $user['email'],
                'phone' => $user['phone'],
                'country' => $user['country'],
                'state' => $user['state'],
                'city' => $user['city'],
                'address' => $user['address'],
            ];
        }
        return $data;
    }
    
    public function getStore($id)
    {
        $store = $this->stores->getElement($id);
        $user = $this->users->getElement($store['owner_id']);
        $data[] = [
            'id' => $store['id'],
            'user_id' => $store['owner_id'],
            'store' => $store['name'],
            'img' => $store['img'],
            'name' => $user['name'],
            'last_name' => $user['last_name'],
            'email' => $user['email'],
            'phone' => $user['phone'],
            'country' => $user['country'],
            'state' => $user['state'],
            'city' => $user['city'],
            'address' => $user['address'],
        ];
        return $data;
    }
    
    public function getStores()
    {
        $stores = $this->stores->getElements();
        $data = [];
        foreach ($stores as $store) {
            $user = $this->users->getElement($store['owner_id']);
            $data[] = [
                'id' => $store['id'],
                'store' => $store['name'],
                'img' => $store['img'],
                'name' => $user['name'],
                'last_name' => $user['last_name'],
                'email' => $user['email'],
                'phone' => $user['phone'],
                'country' => $user['country'],
                'state' => $user['state'],
                'city' => $user['city'],
                'address' => $user['address'],
            ];
        }
        return $data;
    }

}
