<!--start content-->
<main class="page-content">
    <!--breadcrumb-->
    <div class="page-breadcrumb d-none d-sm-flex align-items-center mb-3">
        <a class="breadcrumb-title pe-3" href="/marketing">التسويق</a>
        <div class="ps-3">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb mb-0 p-0">
                    <li class="breadcrumb-item"><a href="javascript:;"><i class="bx bx-home-alt"></i></a>
                    </li>
                    <li class="breadcrumb-item active" aria-current="page">تسويق المنتجات</li>
                </ol>
            </nav>
        </div>
    </div>
    <!--end breadcrumb-->
    <div class="card">
        <div class="card-header py-3">
            <h6 class="mb-0">المنتجات الافضل مبيعاً</h6>
        </div>
        <div class="row">
            <div class="col-12 col-lg-12 d-flex">
                    <div class="card border shadow-none w-100">
                        <div class="card-body">
                            <div class="product-grid">
                                <div class="row row-cols-2 row-cols-lg-6 row-cols-xl-6 row-cols-xxl-6 g-3">
                                    <?php
                                    foreach ($best as $p) {
                                    ?>
                                        <div class="col">
                                            <div class="card border shadow-none mb-0">
                                                <div class="card-body text-center">
                                                    <div class="product-image-wrapper mb-3">
                                                        <img src="/uploads/products/<?php echo $p['store_id'];?>/<?php echo $p['image'];?>" class="img-fluid mb-3 product-image" alt="" />
                                                    </div>
                                                    <h6 class="product-title"><?php echo $p['name'];?></h6>
                                                    <p class="product-price fs-5 mb-1"><span><?php echo $p['price'];?></span> د.ع</p>
                                                    <small><?php echo $p['store'];?></small>
                                                    <div class="actions d-flex align-items-center justify-content-center gap-2 mt-3">
                                                        <a class="dropdown-item text-primary"  href="/marketing/update?id=<?php echo $p['id'];?>&type=best_seller&value=0">رفع من الافضل مبيعاً</a>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    <?php
                                    }
                                    ?>
                                </div><!--end row-->
                            </div>
                        </div>
                    </div>
                </div>
        </div>
    </div>

    <div class="card">
        <div class="card-header py-3">
            <h6 class="mb-0">المنتجات المختارة</h6>
        </div>
        <div class="row">
            <div class="col-12 col-lg-12 d-flex">
                    <div class="card border shadow-none w-100">
                        <div class="card-body">
                            <div class="product-grid">
                                <div class="row row-cols-2 row-cols-lg-6 row-cols-xl-6 row-cols-xxl-6 g-3">
                                    <?php
                                    foreach ($top as $t) {
                                    ?>
                                        <div class="col">
                                            <div class="card border shadow-none mb-0">
                                                <div class="card-body text-center">
                                                    <div class="product-image-wrapper mb-3">
                                                        <img src="/uploads/products/<?php echo $t['store_id'];?>/<?php echo $t['image'];?>" class="img-fluid mb-3 product-image" alt="" />
                                                    </div>
                                                    <h6 class="product-title"><?php echo $t['name'];?></h6>
                                                    <p class="product-price fs-5 mb-1"><span><?php echo $t['price'];?></span> د.ع</p>
                                                    <small><?php echo $t['store'];?></small>
                                                    <div class="actions d-flex align-items-center justify-content-center gap-2 mt-3">
                                                        <a class="dropdown-item text-primary"  href="/marketing/update?id=<?php echo $t['id'];?>&type=top_products&value=0">رفع من المنتجات المختارة</a>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    <?php
                                    }
                                    ?>
                                </div><!--end row-->
                            </div>
                        </div>
                    </div>
                </div>
        </div>
    </div>

    <div class="card">
        <div class="card-header py-3">
            <h6 class="mb-0">جميع المنتجات</h6>
        </div>
        
        <div class="card-body">
            <div class="row">


                <div class="col-12 col-lg-12 d-flex">
                    <div class="card border shadow-none w-100">
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table align-middle">
                                    <thead class="table-light">
                                        <tr>
                                            <th>الرقم</th>
                                            <th>المتجر</th>
                                            <th>المنتج</th>
                                            <th>الفئة</th>
                                            <th>الخيارات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($result as $product) { ?>

                                            <tr>
                                                <td>#<?php echo $product['id'] ?></td>
                                                <td><?php echo $product['store'] ?></td>
                                                <td class="w-auto">
                                                    <div class="d-flex align-items-center gap-2">
                                                        <div class="product-box">
                                                            <img src="/uploads/products/<?php echo $product['store_id'];?>/<?php echo $product['image'];?>" alt="">
                                                        </div>
                                                        <div class="text-wrap">
                                                            <h6 class="mb-0 product-title text-break"><?php echo $product['name'] ?></h6>
                                                            <p class="product-price fs-5 mb-1"><span><?php echo $product['price'];?></span> د.ع</p>
                                                        </div>
                                                    </div>
                                                </td>
                                                <td><?php echo $product['category'] ?></td>
                                                <td>
                                                    <div class="d-flex align-items-center gap-3 fs-6">
                                                        <a class="btn text-primary" data-bs-toggle="dropdown"><i class="lni lni-more-alt"></i></a>
                                                        <div class="dropdown-menu dropdown-menu-right dropdown-menu-lg-end">	
                                                            <a class="dropdown-item"  href="/marketing/update?id=<?php echo $product['id'];?>&type=best_seller&value=1">تحديد ضمن الافضل مبيعاً</a>
                                                            <a class="dropdown-item"  href="/marketing/update?id=<?php echo $product['id'];?>&type=top_products&value=1">تحديد ضمن المنتجات المختارة </a>
                                                        </div>
                                                    </div>
                                                </td>
                                            </tr>
                                            <!-- Modal -->
                                            <div class="modal fade" id="modal-<?php echo $slide['id'] ?>" tabindex="-1" aria-hidden="true">
                                                <div class="modal-dialog modal-lg modal-dialog-centered">
                                                    <div class="modal-content bg-primary">
                                                        <div class="modal-header">
                                                            <h5 class="modal-title text-white">تعديل الشريحة</h5>
                                                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                                        </div>
                                                        <div class="modal-body text-white">
                                                            <div class="card-body">
                                                                <form class="row g-3" method="post" action="/slides/update?id=<?php echo $rate['id'] ?>" enctype="multipart/form-data">
                                                                    <div class="col-12">
                                                                        <label class="form-label">الموضوع</label>
                                                                        <input type="text" name="title" class="form-control" value="<?php echo $slide['title'] ?>">
                                                                    </div>
                                                                    <div class="col-12">
                                                                        <label class="form-label">الرابط</label>
                                                                        <input type="text" name="url" class="form-control" value="<?php echo $slide['url'] ?>">
                                                                    </div>
                                                                    <div class="col-12">
                                                                        <label class="form-label">العنوان</label>
                                                                        <input type="text" name="heading" class="form-control" value="<?php echo $slide['heading'] ?>">
                                                                    </div>
                                                                    <div class="col-12">
                                                                        <label class="form-label">النص</label>
                                                                        <textarea type="number" name="text" class="form-control"><?php echo $slide['text'] ?></textarea>
                                                                    </div>
                                                                    <div class="col-12 m-4">
                                                                        <img src="/uploads/slides/<?php echo $slide['image'] ?>" class="border" height="50" alt="">
                                                                    </div>
                                                                    <div class="col-12">
                                                                        <div class="input-group mb-3 mt-3">
                                                                            <label class="input-group-text" for="inputGroupFile01">الصورة</label>
                                                                            <input type="file" name="file" class="form-control" id="inputGroupFile01">
                                                                        </div>
                                                                    </div>
                                                                    <div class="col-12">
                                                                        <div class="d-flex modal-footer">
                                                                            <button type="submit" class="btn btn-primary" data-bs-dismiss="modal">تعديل</button>
                                                                            <button class="btn btn-primary" data-bs-dismiss="modal">اغلاق</button>
                                                                        </div>
                                                                    </div>
                                                                </form>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                        <?php } ?>
                                    </tbody>
                                </table>
                                <nav class="float-end mt-4" aria-label="Page navigation">
                                    <ul class="pagination">
                                        <li class="page-item <?= $currentPage <= 1 ? 'disabled' : '' ?>">
                                            <a class="page-link" href="?category_id=<?= $_GET['category_id'] ?? '' ?>&page=<?= max(1, $currentPage - 1) ?>">السابق</a>
                                        </li>

                                        <?php
                                        $range = 9; 
                                        $half = floor($range / 2);

                                        if ($totalPages <= $range) {
                                            $start = 1;
                                            $end = $totalPages;
                                        } else {
                                            if ($currentPage <= $half) {
                                                $start = 1;
                                                $end = $range;
                                            } elseif ($currentPage >= $totalPages - $half) {
                                                $start = $totalPages - $range + 1;
                                                $end = $totalPages;
                                            } else {
                                                $start = $currentPage - $half;
                                                $end = $currentPage + $half;
                                            }
                                        }
                                        if ($start > 1) {
                                            echo '<li class="page-item"><a class="page-link" href="?category_id=' . ($_GET['category_id'] ?? '') . '&page=1">1</a></li>';
                                            if ($start > 2) {
                                                echo '<li class="page-item disabled"><span class="page-link">...</span></li>';
                                            }
                                        }
                                        for ($i = $start; $i <= $end; $i++) {
                                            echo '<li class="page-item ' . ($i == $currentPage ? 'active' : '') . '">';
                                            echo '<a class="page-link" href="?category_id=' . ($_GET['category_id'] ?? '') . '&page=' . $i . '">' . $i . '</a>';
                                            echo '</li>';
                                        }
                                        if ($end < $totalPages) {
                                            if ($end < $totalPages - 1) {
                                                echo '<li class="page-item disabled"><span class="page-link">...</span></li>';
                                            }
                                            echo '<li class="page-item"><a class="page-link" href="?category_id=' . ($_GET['category_id'] ?? '') . '&page=' . $totalPages . '">' . $totalPages . '</a></li>';
                                        }
                                        ?>
                                        <li class="page-item <?= $currentPage >= $totalPages ? 'disabled' : '' ?>">
                                            <a class="page-link" href="?category_id=<?= $_GET['category_id'] ?? '' ?>&page=<?= min($totalPages, $currentPage + 1) ?>">التالي</a>
                                        </li>
                                    </ul>
                                </nav>

                            </div>
                        </div>
                    </div>
                </div>
            </div><!--end row-->
        </div>
    </div>

</main>
<!--end page main-->