<?php

class FileModel extends BaseModel {

    public function __construct() {
        parent::__construct();
        $this->table = 'drive_files';
    }

    public function saveFileData(array $fileData) {

        $allowedFields = ['user_id', 'parent_folder_id', 'name', 'path', 'size', 'type', 'description', 'permissions', 'metadata'];
        $fileData = array_intersect_key($fileData, array_flip($allowedFields));

        return $this->insertReturnId($fileData);

    }

    public function getFileById($id){
        return $this->getById($id);
    }

    public function deleteFileById($id){
        return $this->delete($id);
    }
}
