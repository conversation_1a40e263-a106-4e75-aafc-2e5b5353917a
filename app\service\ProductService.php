<?php

class ProductService
{
    private $products;
    private $stores;
    private $categories;
    private $brands;

    public function __construct()
    {
        $this->products = new CrudModel('products');
        $this->stores = new CrudModel('stores');
        $this->categories = new CrudModel('product_categories');
        $this->brands = new CrudModel('brands');
    }
    
    public function getProduct($id)
    {
        $product = $this->products->getElement($id);
        $store = $this->stores->getElement($product['store_id']);
        $category = $this->categories->getElement($product['category_id']);
        $brand = $this->brands->getElement($product['brand_id']);
        $formattedPrice = number_format($product['price'], 0);

        $data[] = [
            'id' => $product['id'],
            'name' => $product['name'],
            'category_id' => $product['category_id'],
            'brand_id' => $product['brand_id'],
            'store_id' => $product['store_id'],
            'description' => $product['description'],
            'image' => $product['image'],
            'price' => $formattedPrice,
            'app_percentage' => $product['app_percentage'],
            'category' => $category['name'],
            'brand' => $brand['name'],
            'store' => $store['name'],
        ];
        return $data;
    }
    
    public function getProducts()
    {
        $products = $this->products->getElements();
        $data = [];
        foreach ($products as $product) {
            $store = $this->stores->getElement($product['store_id']);
            $category = $this->categories->getElement($product['category_id']);
            $brand = $this->brands->getElement($product['brand_id']);
            
            $formattedPrice = (int) $product['price'];
            $formattedAppPercentage = (int) $product['app_percentage'];
    
            $data[] = [
                'id' => $product['id'],
                'name' => $product['name'],
                'category_id' => $product['category_id'],
                'brand_id' => $product['brand_id'],
                'store_id' => $product['store_id'],
                'description' => $product['description'],
                'image' => $product['image'],
                'price' => $formattedPrice,
                'app_percentage' => $formattedAppPercentage,
                'category' => $category['name'],
                'brand' => $brand['name'],
                'store' => $store['name'],
            ];
        }
        return $data;
    }
    

    public function getFilteredProducts($filters, $pagination, $sorting)
    {

        $result = $this->products->getFiltered($filters, $pagination, $sorting);
        $filtered = $result['data'];
        $products = [];
        foreach ($filtered as $product) {
            $store = $this->stores->getElement($product['store_id']);
            $category = $this->categories->getElement($product['category_id']);
            $brand = $this->brands->getElement($product['brand_id']);
            $formattedPrice = number_format($product['price'], 0);

            if ($store['status'] == 1) {
                $products[] = [
                    'id' => $product['id'],
                    'name' => $product['name'],
                    'category_id' => $product['category_id'],
                    'brand_id' => $product['brand_id'],
                    'store_id' => $product['store_id'],
                    'description' => $product['description'],
                    'image' => $product['image'],
                    'price' => $formattedPrice,
                    'app_percentage' => $product['app_percentage'],
                    'category' => $category['name'],
                    'brand' => $brand['name'],
                    'store' => $store['name'],
                ];
            }
        }

        $data['products'] = $products;
        $data['total'] = $result['total'];

        return $data;
        
    }

        
    public function getStoreProducts($store_id)
    {
        $products = $this->products->getElementsWhere('store_id', $store_id);
        $data = [];
        foreach ($products as $product) {
            $store = $this->stores->getElement($product['store_id']);
            $category = $this->categories->getElement($product['category_id']);
            $brand = $this->brands->getElement($product['brand_id']);
            $formattedPrice = number_format($product['price'], 0);

            $data[] = [
                'id' => $product['id'],
                'name' => $product['name'],
                'category_id' => $product['category_id'],
                'brand_id' => $product['brand_id'],
                'store_id' => $product['store_id'],
                'description' => $product['description'],
                'image' => $product['image'],
                'price' => $formattedPrice,
                'app_percentage' => $product['app_percentage'],
                'category' => $category['name'],
                'brand' => $brand['name'],
                'store' => $store['name'],
            ];
        }
        return $data;
    }

}
