<?php
class FileController extends BaseController
{
    private $model;

    public function __construct()
    {
        $this->model = new FileModel;
    }

    public function upload($file, $parent, $description, $userId, $permissions)
    {
        try {
            // Ensure the file array is valid
            if (!isset($file['error']) || is_array($file['error'])) {
                throw new Exception("Invalid file parameters.");
            }

            // Check for file upload errors
            if ($file['error'] !== UPLOAD_ERR_OK) {
                throw new Exception("File upload error: " . $this->fileUploadErrorMessage($file['error']));
            }

            $fileName = basename($file['name']);
            $uploadDir = __DIR__ . '/../../public/uploads/user-files/' . $userId . '/';
            $storageDir = __DIR__ . '/../../storage/user-files/' . $userId . '/';
            $storagePath = '/storage/user-files/' . $userId . '/';

            // Ensure directories exist
            if (!is_dir($uploadDir)) {
                mkdir($uploadDir, 0777, true);
            }
            if (!is_dir($storageDir)) {
                mkdir($storageDir, 0777, true);
            }

            // Check for existing file and modify name if needed
            $fileName = $this->getUniqueFileName($storageDir, $fileName);
            $uploadFile = $uploadDir . $fileName;
            $fileType = mime_content_type($file['tmp_name']);
            $fileSize = $file['size'];

            // Move uploaded file to temporary directory
            if (!move_uploaded_file($file['tmp_name'], $uploadFile)) {
                throw new Exception("File upload failed.");
            }

            if (!$this->validateFile($fileType, $fileSize)) {
                unlink($uploadFile);
                throw new Exception("File validation failed.");
            }

            rename($uploadFile, $storageDir . $fileName);

            $metadata = json_encode([
                'tags' => $_POST['tags'] ?? '',
                'custom_field' => $_POST['custom_field'] ?? ''
            ]);

            $fileData = [
                'user_id' => $userId,
                'parent_folder_id' => $parent,  // Set folder ID if applicable
                'name' => $fileName,
                'path' => $storagePath . $fileName,
                'size' => $fileSize,
                'type' => $fileType,
                'description' => $description,
                'permissions' => $permissions,  // Set access permissions if applicable
                'metadata' => $metadata
            ];

            return $this->model->saveFileData($fileData);
        } catch (Exception $e) {
            $data = [
                'title' => 'Error Occurred',
                'error' => $e->getMessage()
            ];
            // Render a specific error view
            $this->render('../app/view/error.php', $data);
        }
    }

    public function delete($fileId)
    {
        try {
            // Retrieve the file record from the database
            $file = $this->model->getFileById($fileId);

            if (!$file) {
                throw new Exception("File not found.");
            }

            // Check if the user is authorized to delete the file
            if (!$this->authorize($file['user_id'], $file['permissions'])) {
                throw new Exception("Unauthorized access.");
            }

            // Construct the file path
            $filePath = __DIR__ . '/../../storage/user-files/' . $file['user_id'] . '/' . $file['name'];

            // Remove the file from the storage
            if (file_exists($filePath)) {
                unlink($filePath);
            } else {
                throw new Exception("File not found on disk.");
            }

            // Optionally, you could return a success message or status
            return json_encode(['success' => true, 'message' => 'File deleted successfully.']);
        } catch (Exception $e) {
            return json_encode(['status' => 'error', 'message' => $e->getMessage()]);
        }
    }

    private function getUniqueFileName($directory, $fileName)
    {
        $fileInfo = pathinfo($fileName);
        $baseName = $fileInfo['filename'];
        $extension = isset($fileInfo['extension']) ? '.' . $fileInfo['extension'] : '';
        $newFileName = $fileName;
        $counter = 1;

        while (file_exists($directory . $newFileName)) {
            $newFileName = $baseName . '_' . $counter . $extension;
            $counter++;
        }

        return $newFileName;
    }

    public function download($fileId)
    {
        try {
            $file = $this->model->getFileById($fileId);

            if (!$file) {
                throw new Exception("File not found.");
            }

            $filePath = $file['path'];
            $fileName = $file['name'];

            if (!file_exists($filePath)) {
                throw new Exception("File not found on disk.");
            }

            header('Content-Description: File Transfer');
            header('Content-Type: application/octet-stream');
            header('Content-Disposition: attachment; filename="' . basename($fileName) . '"');
            header('Expires: 0');
            header('Cache-Control: must-revalidate');
            header('Pragma: public');
            header('Content-Length: ' . filesize($filePath));

            readfile($filePath);
            exit;
        } catch (Exception $e) {
            $data = [
                'title' => 'Error Occurred',
                'error' => $e->getMessage()
            ];
            // Render a specific error view
            $this->render('../app/view/error.php', $data);
        }
    }

    private function fileUploadErrorMessage($error_code)
    {
        switch ($error_code) {
            case UPLOAD_ERR_INI_SIZE:
                return "The uploaded file exceeds the upload_max_filesize directive in php.ini.";
            case UPLOAD_ERR_FORM_SIZE:
                return "The uploaded file exceeds the MAX_FILE_SIZE directive that was specified in the HTML form.";
            case UPLOAD_ERR_PARTIAL:
                return "The uploaded file was only partially uploaded.";
            case UPLOAD_ERR_NO_FILE:
                return "No file was uploaded.";
            case UPLOAD_ERR_NO_TMP_DIR:
                return "Missing a temporary folder.";
            case UPLOAD_ERR_CANT_WRITE:
                return "Failed to write file to disk.";
            case UPLOAD_ERR_EXTENSION:
                return "A PHP extension stopped the file upload.";
            default:
                return "Unknown upload error.";
        }
    }

    private function validateFile($fileType, $fileSize)
    {
        $allowedTypes = [
            'image/jpeg', 'image/png', 'application/pdf', 'image/svg+xml', 
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        ];
        $maxSize = 10485760; // 10MB

        if (!in_array($fileType, $allowedTypes)) {
            return false;
        }

        if ($fileSize > $maxSize) {
            return false;
        }

        return true;
    }

    public function viewFile($fileId) {
        $file = $this->model->getFileById($fileId);

        if ($file && $this->authorize($file['user_id'], $file['permissions'])) {
            $filePath = __DIR__ . '/../../storage/user-files/' . $file['user_id'] . '/' . $file['name'];

            if (file_exists($filePath)) {
                $this->serveFile($filePath, $file['name']);
            } else {
                echo "File not found.";
            }
        } else {
            echo "Access Denied.";
        }
    }

    private function authorize($fileOwnerId, $permissions) {
        // Check if the current user is authorized to view the file
        if ($_SESSION['user_id'] === $fileOwnerId) {
            return true;
        }elseif($permissions == 3){
            return true;
        }else{
            return false;
        }
    }

    private function serveFile($filePath, $fileName) {
        $fileType = mime_content_type($filePath);
        header('Content-Type: ' . $fileType);
        header('Content-Disposition: inline; filename="' . basename($fileName) . '"');
        readfile($filePath);
    }
}

