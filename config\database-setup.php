<?php
/**
 * Wardena API Database Setup Script
 * This script creates and configures the database for the Wardena application
 */

// Load configuration
require_once __DIR__ . '/config.php';

class DatabaseSetup {
    
    private $host;
    private $username;
    private $password;
    private $dbName;
    private $pdo;
    
    public function __construct() {
        $this->host = DB_HOST;
        $this->username = DB_USER;
        $this->password = DB_PASS;
        $this->dbName = DB_NAME;
    }
    
    /**
     * Main setup method
     */
    public function setup() {
        try {
            echo "🚀 Starting Wardena Database Setup...\n\n";
            
            // Step 1: Connect to MySQL server
            $this->connectToServer();
            
            // Step 2: Create database if not exists
            $this->createDatabase();
            
            // Step 3: Select database
            $this->selectDatabase();
            
            // Step 4: Run SQL file
            $this->runSqlFile();
            
            // Step 5: Verify installation
            $this->verifyInstallation();
            
            echo "\n✅ Database setup completed successfully!\n";
            echo "🔐 Default admin credentials:\n";
            echo "   Username: admin\n";
            echo "   Email: <EMAIL>\n";
            echo "   Password: password (Please change this immediately!)\n\n";
            
        } catch (Exception $e) {
            echo "\n❌ Error during setup: " . $e->getMessage() . "\n";
            exit(1);
        }
    }
    
    /**
     * Connect to MySQL server
     */
    private function connectToServer() {
        try {
            echo "📡 Connecting to MySQL server...\n";
            $this->pdo = new PDO("mysql:host={$this->host}", $this->username, $this->password);
            $this->pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            $this->pdo->exec("SET NAMES utf8mb4");
            echo "✅ Connected to MySQL server successfully\n";
        } catch (PDOException $e) {
            throw new Exception("Failed to connect to MySQL server: " . $e->getMessage());
        }
    }
    
    /**
     * Create database if it doesn't exist
     */
    private function createDatabase() {
        try {
            echo "🗄️  Creating database '{$this->dbName}'...\n";
            
            // Check if database exists
            $stmt = $this->pdo->prepare("SELECT SCHEMA_NAME FROM INFORMATION_SCHEMA.SCHEMATA WHERE SCHEMA_NAME = ?");
            $stmt->execute([$this->dbName]);
            
            if ($stmt->rowCount() === 0) {
                // Create database with proper charset and collation
                $sql = "CREATE DATABASE `{$this->dbName}` 
                        CHARACTER SET utf8mb4 
                        COLLATE utf8mb4_unicode_ci";
                $this->pdo->exec($sql);
                echo "✅ Database '{$this->dbName}' created successfully\n";
            } else {
                echo "ℹ️  Database '{$this->dbName}' already exists\n";
            }
        } catch (PDOException $e) {
            throw new Exception("Failed to create database: " . $e->getMessage());
        }
    }
    
    /**
     * Select the database
     */
    private function selectDatabase() {
        try {
            echo "🎯 Selecting database '{$this->dbName}'...\n";
            $this->pdo->exec("USE `{$this->dbName}`");
            echo "✅ Database selected successfully\n";
        } catch (PDOException $e) {
            throw new Exception("Failed to select database: " . $e->getMessage());
        }
    }
    
    /**
     * Run the SQL file to create tables
     */
    private function runSqlFile() {
        $sqlFile = __DIR__ . '/general.sql';
        
        if (!file_exists($sqlFile)) {
            throw new Exception("SQL file not found: {$sqlFile}");
        }
        
        echo "📋 Running SQL file to create tables...\n";
        
        $sql = file_get_contents($sqlFile);
        
        // Split SQL into individual statements
        $statements = $this->splitSqlStatements($sql);
        
        $successCount = 0;
        $errorCount = 0;
        
        foreach ($statements as $statement) {
            $statement = trim($statement);
            if (empty($statement) || strpos($statement, '--') === 0) {
                continue;
            }
            
            try {
                $this->pdo->exec($statement);
                $successCount++;
                
                // Extract table name for progress indication
                if (preg_match('/CREATE TABLE.*?`?(\w+)`?/i', $statement, $matches)) {
                    echo "  ✅ Created table: {$matches[1]}\n";
                } elseif (preg_match('/INSERT INTO.*?`?(\w+)`?/i', $statement, $matches)) {
                    echo "  ✅ Inserted data into: {$matches[1]}\n";
                } elseif (preg_match('/CREATE.*?VIEW.*?`?(\w+)`?/i', $statement, $matches)) {
                    echo "  ✅ Created view: {$matches[1]}\n";
                }
                
            } catch (PDOException $e) {
                $errorCount++;
                echo "  ⚠️  Warning: " . $e->getMessage() . "\n";
            }
        }
        
        echo "📊 SQL execution completed: {$successCount} successful, {$errorCount} warnings\n";
    }
    
    /**
     * Split SQL content into individual statements
     */
    private function splitSqlStatements($sql) {
        // Remove comments
        $sql = preg_replace('/--.*$/m', '', $sql);
        $sql = preg_replace('/\/\*.*?\*\//s', '', $sql);
        
        // Split by semicolon, but be careful with semicolons inside strings
        $statements = [];
        $current = '';
        $inString = false;
        $stringChar = '';
        
        for ($i = 0; $i < strlen($sql); $i++) {
            $char = $sql[$i];
            
            if (!$inString && ($char === '"' || $char === "'")) {
                $inString = true;
                $stringChar = $char;
            } elseif ($inString && $char === $stringChar) {
                $inString = false;
            } elseif (!$inString && $char === ';') {
                $statements[] = $current;
                $current = '';
                continue;
            }
            
            $current .= $char;
        }
        
        if (!empty(trim($current))) {
            $statements[] = $current;
        }
        
        return $statements;
    }
    
    /**
     * Verify the installation by checking key tables
     */
    private function verifyInstallation() {
        echo "🔍 Verifying installation...\n";
        
        $requiredTables = [
            'users', 'stores', 'products', 'orders', 'order_details',
            'notifications', 'admin_settings', 'visits'
        ];
        
        $missingTables = [];
        
        foreach ($requiredTables as $table) {
            try {
                $stmt = $this->pdo->query("SELECT 1 FROM `{$table}` LIMIT 1");
                echo "  ✅ Table '{$table}' exists and accessible\n";
            } catch (PDOException $e) {
                $missingTables[] = $table;
                echo "  ❌ Table '{$table}' is missing or inaccessible\n";
            }
        }
        
        if (!empty($missingTables)) {
            throw new Exception("Missing required tables: " . implode(', ', $missingTables));
        }
        
        // Check if admin user exists
        try {
            $stmt = $this->pdo->prepare("SELECT COUNT(*) FROM users WHERE role = 'admin'");
            $stmt->execute();
            $adminCount = $stmt->fetchColumn();
            
            if ($adminCount > 0) {
                echo "  ✅ Admin user exists\n";
            } else {
                echo "  ⚠️  No admin user found\n";
            }
        } catch (PDOException $e) {
            echo "  ⚠️  Could not verify admin user: " . $e->getMessage() . "\n";
        }
    }
    
    /**
     * Get database statistics
     */
    public function getStats() {
        try {
            $this->pdo = new PDO("mysql:host={$this->host};dbname={$this->dbName}", 
                               $this->username, $this->password);
            $this->pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            
            echo "\n📊 Database Statistics:\n";
            echo "========================\n";
            
            // Get table count
            $stmt = $this->pdo->query("SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = '{$this->dbName}'");
            $tableCount = $stmt->fetchColumn();
            echo "Tables: {$tableCount}\n";
            
            // Get user count
            $stmt = $this->pdo->query("SELECT COUNT(*) FROM users");
            $userCount = $stmt->fetchColumn();
            echo "Users: {$userCount}\n";
            
            // Get store count
            $stmt = $this->pdo->query("SELECT COUNT(*) FROM stores");
            $storeCount = $stmt->fetchColumn();
            echo "Stores: {$storeCount}\n";
            
            // Get product count
            $stmt = $this->pdo->query("SELECT COUNT(*) FROM products");
            $productCount = $stmt->fetchColumn();
            echo "Products: {$productCount}\n";
            
            echo "\n";
            
        } catch (Exception $e) {
            echo "Could not retrieve statistics: " . $e->getMessage() . "\n";
        }
    }
}

// Run setup if called directly
if (php_sapi_name() === 'cli' || basename($_SERVER['PHP_SELF']) === 'database-setup.php') {
    $setup = new DatabaseSetup();
    
    if (isset($_GET['stats']) || (isset($argv[1]) && $argv[1] === 'stats')) {
        $setup->getStats();
    } else {
        $setup->setup();
    }
}
?>
