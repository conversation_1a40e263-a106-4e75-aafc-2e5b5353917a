<?php

class StoresController extends BaseController
{

    private $model;
    private $storeService;
    private $users;

    public function __construct($request, $response)
    {
        parent::__construct($request, $response);
        $this->model = new CrudModel('stores');
        $this->storeService = new StoreService();
        $this->users = new CrudModel('users');
    }

    public function index()
    {
        if ($this->isLoggedIn()) {
            $userId = $this->getSessionData('user_id');
            $user = $this->users->getElement($userId);
            $stores = $this->storeService->getStores();
            $data = [
                'title' => 'المتاجر',
                'style' => null,
                'user' => $user,
                'stores' => $stores,
            ];

            $this->render('../app/view/admin/stores.php', $data);
        } else {
            $this->redirect('/login');
        }
    }

    public function edit()
    {
        if ($this->isLoggedIn()) {
            $id = $_GET['store'];
            $userId = $this->getSessionData('user_id');
            $user = $this->users->getElement($userId);
            $result = $this->storeService->getStore($id);
            $data = [
                'title' => 'تعديل المتجر',
                'style' => null,
                'user' => $user,
                'result' => $result,
            ];

            $this->render('../app/view/admin/edit-store.php', $data);
        } else {
            $this->redirect('/login');
        }
    }

    public function updateStoreImg(){
        try {
            $this->model->beginTransaction();

            $id = $_POST['id'];

            $file = $_FILES['file'];

            if (!isset($file['error']) || is_array($file['error'])) {

                $validationErrors['file'] = "Invalid file parameters.";
                $data = [
                    'errors' => $validationErrors,
                    'old' => null,
                ];
                $this->redirect('/stores', $data);

            }

            if ($file['error'] !== UPLOAD_ERR_OK) {
                $validationErrors['file'] = "File upload error: " . $this->fileUploadErrorMessage($file['error']);
                $data = [
                    'errors' => $validationErrors,
                    'old' => null,
                ];
                $this->redirect('/stores', $data);
            }
            $fileName = basename($file['name']);

            $uploadDir = __DIR__ . '/../../public/uploads/stores/';
            if (!is_dir($uploadDir)) {
                mkdir($uploadDir, 0777, true);
            }
            $fileName = $this->getUniqueFileName($uploadDir, $fileName);
            $uploadFile = $uploadDir . $fileName;
            $fileType = mime_content_type($file['tmp_name']);
            $fileSize = $file['size'];

            if (!move_uploaded_file($file['tmp_name'], $uploadFile)) {

                $validationErrors['file'] = "File upload failed.";
                $data = [
                    'errors' => $validationErrors,
                    'old' => null,
                ];
                $this->redirect('/stores', $data);

            }

            if (!$this->validateFile($fileType, $fileSize)) {
                unlink($uploadFile);

                $validationErrors['file'] = "File validation failed.";
                $data = [
                    'errors' => $validationErrors,
                    'old' => null,
                ];
                $this->redirect('/stores', $data);
                
            }

            $img = $fileName;
            $formData['img'] = $img;

            if (!$this->model->updateElement($formData, $id)) {
                $this->model->rollBack();
                return false;
            } else {
                $this->model->commit();
                $this->redirect('/stores');
                return;
            }
        } catch (Exception $e) {
            $this->model->rollBack();
            $this->logError($e->getMessage());
            return $this->redirect('/error');
        }
    }

    public function delete()
    {
        try {
            $this->model->beginTransaction();

            $id = $_GET['store'];
            $user_id = $this->users->getElement($id);

            if (!$this->model->deleteElement($id)) {
                $this->model->rollBack();
                return false;
            } else {
                if (!$this->users->deleteElement($user_id)) {
                    $this->model->rollBack();
                    return false;
                }
                $this->model->commit();
                $this->redirect('/stores');
                return;
            }
        } catch (Exception $e) {
            $this->model->rollBack();
            $this->logError($e->getMessage());
            return $this->redirect('/error');
        }
    }

}
