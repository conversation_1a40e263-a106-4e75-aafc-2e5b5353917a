<?php

class ApiService 
{
    private $model;
    private $auth;
    private $stores;
    private $slides;
    private $categories;
    private $products;
    private $productService;
    private $storeService;


    public function __construct()
    {
        $this->model = new DashboardModel();
        $this->auth = new AuthService();
        $this->stores = new CrudModel('stores');
        $this->slides = new CrudModel('slides');
        $this->categories = new CrudModel('product_categories');
        $this->products = new CrudModel('products');
        $this->productService = new ProductService();
        $this->storeService = new StoreService();

    }

    public function getFilteredProducts($filters, $pagination, $sorting)
    {

        $result = $this->productService->getFilteredProducts($filters, $pagination, $sorting);
        $products = $result['products'];
        $data = [];
        foreach ($products as $product) {
            $imageUrl = 'https://wardena.app/uploads/products/'.$product['store_id'].'/'.$product['image'];

            $formattedPrice = (int) $product['price'];
            $formattedAppPercentage = (int) $product['app_percentage'];

            $data[] = [
                'id' => (int) $product['id'],
                'name' => $product['name'],
                'category_id' => $product['category_id'],
                'brand_id' => $product['brand_id'],
                'store_id' => $product['store_id'],
                'description' => $product['description'],
                'image' => $imageUrl,
                'price' => $formattedPrice,
                'app_percentage' => $formattedAppPercentage,
                'category' => $product['category'],
                'brand' => $product['brand'],
                'store' => $product['store'],
            ];
        }

        return $data;
        
    }

    public function getProducts()
    {

        $products = $this->productService->getProducts();
        $data = [];
        foreach ($products as $product) {

            $imageUrl = 'https://wardena.app/uploads/products/'.$product['store_id'].'/'.$product['image'];

            $data[] = [
                'id' => $product['id'],
                'name' => $product['name'],
                'category_id' => $product['category_id'],
                'brand_id' => $product['brand_id'],
                'store_id' => $product['store_id'],
                'description' => $product['description'],
                'image' => $imageUrl,
                'price' => $product['price'],
                'app_percentage' => $product['app_percentage'],
                'category' => $product['category'],
                'brand' => $product['brand'],
                'store' => $product['store'],
            ];
        }

        return $data;

    }

    public function getCategories()
    {

        $categories = $this->categories->getElements();
        $data = [];
        foreach ($categories as $category) {

            $imageUrl = 'https://wardena.app/uploads/categories/'.$category['img'];

            $data[] = [
                'id' => $category['id'],
                'name' => $category['name'],
                'parent' => $category['parent'],
                'image' => $imageUrl,
            ];
        }

        return $data;

    }

    public function getSlides()
    {

        $slides = $this->slides->getElements();
        $data = [];
        foreach ($slides as $slide) {

            $imageUrl = 'https://wardena.app/uploads/slides/'.$slide['image'];

            $data[] = [
                'id' => $slide['id'],
                'title' => $slide['title'],
                'heading' => $slide['heading'],
                'text' => $slide['text'],
                'url' => $slide['url'],
                'image' => $imageUrl,
            ];
        }

        return $data;

    }

    public function getShops()
    {

        $shops = $this->stores->getElements();
        $data = [];
        foreach ($shops as $shop) {

            $imageUrl = 'https://wardena.app/uploads/stores/'.$shop['img'];

            $data[] = [
                'id' => $shop['id'],
                'name' => $shop['name'],
                'image' => $imageUrl,
            ];
        }

        return $data;

    }
}