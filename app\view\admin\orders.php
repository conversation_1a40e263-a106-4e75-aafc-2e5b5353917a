<!--start content-->
<main class="page-content">
    <!--breadcrumb-->
    <div class="page-breadcrumb d-none d-sm-flex align-items-center mb-3">
        <div class="breadcrumb-title pe-3">الطلبات</div>
        <div class="ps-3">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb mb-0 p-0">
                    <li class="breadcrumb-item"><a href="javascript:;"><i class="bx bx-home-alt"></i></a>
                    </li>
                    <li class="breadcrumb-item active" aria-current="page">طلبات العملاء</li>
                </ol>
            </nav>
        </div>
    </div>
    <!--end breadcrumb-->
    <div class="row">
        <div class="col-12 col-lg-9 d-flex">
            <div class="card w-100">
                <div class="card-header py-3">
                    <div class="row g-3">
                        <div class="col-lg-4 col-md-6 me-auto">
                            <div class="ms-auto position-relative">
                                <div class="position-absolute top-50 translate-middle-y search-icon px-3"><i class="bi bi-search"></i></div>
                                <input class="form-control ps-5" type="text" placeholder="بحث الطلبات">
                            </div>
                        </div>
                        <div class="col-lg-2 col-6 col-md-3">
                            <select class="form-select">
                            <option value="10">اظهار 10 نتيجة</option>
                            <option value="30">اظهار 30 نتيجة</option>
                            <option value="50">اظهار 50 نتيجة</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table align-middle">
                            <?php
                            if (empty($result)) {
                                echo 'لايوجد طلبات حتى الان';
                            } else {
                            ?>
                                <thead class="table-light">
                                    <tr>
                                        <th>رقم الطلب</th>
                                        <th>اسم الزبون</th>
                                        <th>العنوان</th>
                                        <th>مندوب التوصيل</th>
                                        <th>الحالة</th>
                                        <th>التاريخ</th>
                                        <th>الخيارات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php
                                    foreach ($result as $order) {
                                    ?>
                                        <tr>
                                            <td><?php echo $order['id']; ?></td>
                                            <td><?php echo $order['customer_name']; ?></td>
                                            <td><?php echo $order['customer_phone']; ?></td>
                                            <td>
                                                <input type="hidden" class="order-id" value="<?php echo $order['id']; ?>">
                                                <select name="delivery_name" class="form-select delivery-select" data-order-id="<?php echo $order['id']; ?>" onchange="assign(this)">
                                                    <option value=""><?php echo $order['delivery_name']; ?></option>
                                                    <?php
                                                    foreach ($delivery as $boy) {
                                                        $option = ($order['delivery_id'] == $boy['id']) ? 'selected' : '';
                                                        echo '<option value="'. $boy['id'] .'" '. $option .'>'. $boy['name'] .'</option>';
                                                    }
                                                    ?>
                                                </select>
                                            </td>
                                            <td><span class="badge <?php echo $order['status']['class']; ?> text-success w-100"><?php echo $order['status']['arabic']; ?></span></td>
                                            <td><?php echo $order['date']; ?></td>
                                            <td>
                                                <div class="d-flex align-items-center gap-3 fs-6">
                                                    <a href="#" class="text-primary open-edit-modal" data-order-id="<?php echo $order['id'] ?>" data-bs-toggle="modal" data-bs-target="#edit-modal"> <i class="bi bi-eye-fill"></i> </a>
                                                    <a href="/orders/delete?id=<?php echo $order['id'] ?>" class="text-danger"><i class="bi bi-trash-fill"></i></a>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php
                                    }
                                    ?>
                                </tbody>
                            <?php
                            }
                            ?>
                        </table>
                    </div>
                    <nav class="float-end mt-4" aria-label="Page navigation">
                        <ul class="pagination">
                            <li class="page-item disabled"><a class="page-link" href="#">السابق</a></li>
                            <li class="page-item active"><a class="page-link" href="#">1</a></li>
                            <li class="page-item"><a class="page-link" href="#">2</a></li>
                            <li class="page-item"><a class="page-link" href="#">3</a></li>
                            <li class="page-item"><a class="page-link" href="#">التالي</a></li>
                        </ul>
                    </nav>
                </div>
            </div>
        </div>
        <div class="col-12 col-lg-3 d-flex">
            <div class="card w-100">
                <div class="card-header py-3">
                    <h5 class="mb-0">فلترة</h5>
                </div>
                <div class="card-body">
                    <form class="row g-3">
                        <div class="col-12">
                            <label class="form-label">رقم الطلب</label>
                            <input type="text" class="form-control" placeholder="رقم الطلب">
                        </div>
                        <div class="col-12">
                            <label class="form-label">الزبون</label>
                            <input type="text" class="form-control" placeholder="اسم الزبون">
                        </div>
                        <div class="col-12">
                            <label class="form-label">حالة الطلب</label>
                            <select class="form-select">
                                <option>اختر حالة</option>
                                <option value="pending">قيد المراجعة</option>
                                <option value="accepted">جار التحضير</option>
                                <option value="in_transit">في الطريق</option>
                                <option value="delivered">تم التوصيل</option>
                                <option value="canceled">تم استرجاع الطلب</option>
                            </select>
                        </div>
                        <div class="col-12">
                            <div class="d-grid">
                                <button class="btn btn-primary">فلترة الطلبات</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div><!--end row-->

</main>
<!--end page main-->

<!--end page main-->
<div class="modal fade" id="edit-modal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-xl modal-dialog-centered">
        <div class="modal-content bg-white">
            <div class="modal-header">
                <h5 class="modal-title">عرض الطلب</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="order-modal-body">

            </div>
        </div>
    </div>
</div>

<script>
  document.addEventListener('DOMContentLoaded', function () {
    document.querySelectorAll('.open-edit-modal').forEach(function (editBtn) {
      editBtn.addEventListener('click', function () {
        let orderId = this.getAttribute('data-order-id');
        const modal = document.getElementById('order-modal-body');

        // Fetch order details via AJAX (example placeholder)
        fetch(`/orders/get-order?id=${orderId}`)
          .then(response => response.json())
          .then(res => {
            let productsHtml = '';
            res.data.products.forEach(product => {
                productsHtml += `
                <tr>
                    <td>
                    <div class="orderlist">
                        <a class="d-flex align-items-center gap-2" href="javascript:;">
                        <div class="product-box">
                            <img src="/uploads/products/${product.store_id}/${product.img}" alt="">
                        </div>
                        <div>
                            <p class="mb-0 product-title">${product.name}</p>
                            <p class="mb-0"><strong>المتجر:</strong> ${product.store}</p>
                            <p class="mb-0"><strong>الماركة:</strong> ${product.brand}</p>
                        </div>
                        </a>
                    </div>
                    </td>
                    <td>${product.price} د.ع</td>
                    <td>${product.qty}</td>
                    <td>${product.total} د.ع</td>
                </tr>
                `;
            });
            let html = `
                <div class="card-header py-3"> 
                  <div class="row g-3 align-items-center">
                    <div class="col-12 col-lg-4 col-md-6 me-auto">
                        <h5 class="mb-1">${res.data.date}</h5>
                        <p class="mb-0">رقم الطلب : #${res.data.id}</p>
                    </div>
                    <div class="col-12 col-lg-3 col-6 col-md-3">
                        <select class="form-select" id="status-select">
                            <option value="pending" ${res.data.status.english === 'pending' ? 'selected' : ''}>في الانتظار</option>
                            <option value="accepted" ${res.data.status.english === 'accepted' ? 'selected' : ''}>قيد التحضير</option>
                            <option value="in_transit" ${res.data.status.english === 'in_transit' ? 'selected' : ''}>في الطريق</option>
                            <option value="delivered" ${res.data.status.english === 'delivered' ? 'selected' : ''}>تم التوصيل</option>
                            <option value="canceled" ${res.data.status.english === 'canceled' ? 'selected' : ''}>ملغي</option>
                        </select>
                    </div>
                    <div class="col-12 col-lg-3 col-6 col-md-3">
                        <button class="btn btn-primary" id="save-status-btn">حفظ</button>
                    </div>
                  </div>
              </div>
              <div class="card-body">
                  <div class="row row-cols-1 row-cols-xl-2 row-cols-xxl-3">
                      <div class="col">
                          <div class="card border shadow-none radius-10">
                              <div class="card-body">
                                  <div class="d-flex align-items-center gap-3">
                                      <div class="icon-box bg-light-primary border-0">
                                          <i class="bi bi-person text-primary"></i>
                                      </div>
                                      <div class="info">
                                          <h6 class="mb-2">الزبون</h6>
                                          <p class="mb-1">${res.data.customer_name} ${res.data.customer_last_name}</p>
                                          <p class="mb-1">${res.data.customer_phone}</p>
                                          <p class="mb-1">${res.data.address}</p>
                                      </div>
                                  </div>
                              </div>
                          </div>
                      </div>
                      <div class="col">
                          <div class="card border shadow-none radius-10">
                              <div class="card-body">
                                  <div class="d-flex align-items-center gap-3">
                                      <div class="icon-box bg-light-success border-0">
                                          <i class="bi bi-truck text-success"></i>
                                      </div>
                                      <div class="info">
                                          <h6 class="mb-2">معلومات الطلب</h6>
                                          <p class="mb-1"><strong>مندوب التوصيل</strong> : ${res.data.delivery_name}</p>
                                          <p class="mb-1"><strong>المبلغ الإجمالي</strong> : ${res.data.total_amount} د.ع</p>
                                          <p class="mb-1"><strong>حالة الطلب</strong> : ${res.data.status.arabic}</p>
                                      </div>
                                  </div>
                              </div>
                          </div>
                      </div>
                  </div>
                  <div class="row">
                      <div class="col-12 col-lg-8">
                          <div class="card border shadow-none radius-10">
                              <div class="card-body">
                                  <div class="table-responsive">
                                      <table class="table align-middle mb-0">
                                          <thead class="table-light">
                                              <tr>
                                                  <th>المنتج</th>
                                                  <th>السعر</th>
                                                  <th>الكمية</th>
                                                  <th>المجموع</th>
                                              </tr>
                                          </thead>
                                          <tbody>
                                              ${productsHtml}
                                          </tbody>
                                      </table>
                                  </div>
                              </div>
                          </div>
                      </div>
                      <div class="col-12 col-lg-4">
                          <div class="card border shadow-none bg-light radius-10">
                              <div class="card-body">
                                  <div class="d-flex align-items-center mb-4">
                                      <div>
                                          <h5 class="mb-0">ملخص الطلب</h5>
                                      </div>
                                      <div class="ms-auto">
                                          <button type="button" class="btn alert-success radius-30 px-4">${res.data.status.arabic}</button>
                                      </div>
                                  </div>
                                  <div class="d-flex align-items-center mb-3">
                                      <div><p class="mb-0">المبلغ الكلي</p></div>
                                      <div class="ms-auto"><h5 class="mb-0">${res.data.total_amount} د.ع</h5></div>
                                  </div>
                                  <div class="d-flex align-items-center mb-3">
                                      <div><p class="mb-0">سعر الشحن</p></div>
                                      <div class="ms-auto"><h5 class="mb-0">${res.data.delivery_fee} د.ع</h5></div>
                                  </div>
                                  <div class="d-flex align-items-center mb-3">
                                      <div><p class="mb-0">أرباح التطبيق</p></div>
                                      <div class="ms-auto"><h5 class="mb-0">${res.data.total_app_earnings} د.ع</h5></div>
                                  </div>
                                  <div class="d-flex align-items-center mb-3">
                                      <div><p class="mb-0">أرباح المتجر</p></div>
                                      <div class="ms-auto"><h5 class="mb-0">${res.data.total_store_earnings} د.ع</h5></div>
                                  </div>
                              </div>
                          </div>
                      </div>
                  </div>
              </div>
            `;
            modal.innerHTML  = html;
            document.getElementById('save-status-btn').addEventListener('click', function () {
                const status = document.getElementById('status-select').value;
                fetch('/orders/update', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    form: {
                        order_id: orderId,
                        status: status,
                    }
                }),
                })
                .then(response => response.json())
                .then(result => {
                alert(result.message);
                location.reload();
                })
                .catch(error => console.error('Error updating status:', error));
            });
          })
          .catch(error => console.error('Error fetching order:', error));
      });
    });
  });

  function assign(selectElement) {
    let orderId = selectElement.closest('tr').querySelector('.order-id').value;
    let value = selectElement.value;

    fetch('/orders/assign', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json',
    },
    body: JSON.stringify({
        form: {
            order_id: orderId,
            delivery: value,
        }
    }),
    })
    .then(response => response.json())
    .then(result => {
    alert(result.message);
    location.reload();
    })
    .catch(error => console.error('Error updating status:', error));
}

</script>