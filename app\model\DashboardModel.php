<?php

class DashboardModel extends BaseModel
{

    public function __construct() {
        parent::__construct();
        $this->table = 'users';
    }

    public function getUserData($userId) {

        return $this->getById($userId);

    }

    public function getVisitsByDay() {
        $sql = "
            SELECT 
                DATE(visit_time) as visit_date,
                COUNT(DISTINCT ip_address) as unique_visits,
                COUNT(*) as total_views
            FROM visits
            GROUP BY visit_date
            ORDER BY visit_date ASC
        ";
        try {
            $stmt = $this->db->prepare($sql);
            $stmt->execute();
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            error_log('Failed to fetch users: ' . $e->getMessage());
            throw $e;
        }
    }
    
}
