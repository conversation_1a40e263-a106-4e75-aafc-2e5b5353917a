# Wardena API 🚀

**Developer**: [<PERSON>](https://pearlfibers.com/) | **Version**: 1.0.0

نظام إدارة متاجر ومنتجات وطلبات متكامل مع نظام توصيل

## المميزات الرئيسية ✨

- 🏪 **إدارة المتاجر**: نظام شامل لإدارة المتاجر وأصحابها
- 📦 **إدارة المنتجات**: كتالوج منتجات مع فئات وعلامات تجارية
- 🛒 **نظام الطلبات**: معالجة الطلبات من البداية للنهاية
- 🚚 **نظام التوصيل**: إدارة عمال التوصيل والمدفوعات
- 👥 **إدارة المستخدمين**: أدوار متعددة (أدمن، صاحب متجر، عميل، موصل)
- 💰 **نظام المدفوعات**: تتبع الأرباح والمدفوعات
- 📊 **التحليلات**: إحصائيات مفصلة وتقارير
- 🔔 **الإشعارات**: نظام إشعارات متقدم
- 🔒 **الأمان**: حماية متقدمة وتشفير البيانات

## التقنيات المستخدمة 🛠️

- **Backend**: PHP 8.0+ مع PDO
- **Database**: MySQL 8.0+ مع تحسينات الأداء
- **Architecture**: MVC Pattern مع Singleton للاتصال بقاعدة البيانات
- **Security**: JWT Tokens، Password Hashing، SQL Injection Protection
- **API**: RESTful API مع CORS Support
- **Caching**: File-based Caching System
- **Logging**: Comprehensive Error Logging

## متطلبات النظام 📋

- PHP 7.4+ (يُفضل 8.0+)
- MySQL 5.7+ (يُفضل 8.0+)
- Apache/Nginx مع mod_rewrite
- 500MB مساحة تخزين
- 128MB ذاكرة RAM

## التثبيت السريع ⚡

```bash
# 1. استنساخ المشروع
git clone https://github.com/your-repo/wardena-api.git
cd wardena-api

# 2. إعداد البيئة
cp .env.example .env

# 3. تحديث إعدادات قاعدة البيانات في .env
# 4. تشغيل إعداد قاعدة البيانات
php config/database-setup.php

# 5. إعداد الصلاحيات
chmod 755 public/uploads/ storage/
```

## بنية المشروع 📁

```
wardena-api/
├── app/
│   ├── controller/     # Controllers
│   ├── model/         # Models
│   ├── service/       # Business Logic
│   └── view/          # Views
├── config/
│   ├── config.php     # Configuration
│   ├── database-setup.php  # DB Setup
│   ├── general.sql    # Database Schema
│   └── hosting-setup.md    # Hosting Guide
├── core/
│   ├── Database.php   # Database Connection
│   ├── BaseModel.php  # Base Model Class
│   ├── BaseController.php  # Base Controller
│   └── Router.php     # URL Routing
├── public/
│   ├── index.php      # Entry Point
│   ├── uploads/       # File Uploads
│   └── assets/        # Static Assets
├── storage/           # Cache & Logs
├── vendor/           # Third-party Libraries
├── .env              # Environment Variables
├── .htaccess         # Apache Configuration
└── README.md         # This File
```

## API Endpoints 🌐

### Authentication
- `POST /api/auth/login` - تسجيل الدخول
- `POST /api/auth/register` - التسجيل
- `POST /api/auth/logout` - تسجيل الخروج
- `POST /api/auth/refresh` - تجديد Token

### Users
- `GET /api/users` - قائمة المستخدمين
- `GET /api/users/{id}` - تفاصيل مستخدم
- `POST /api/users` - إنشاء مستخدم
- `PUT /api/users/{id}` - تحديث مستخدم
- `DELETE /api/users/{id}` - حذف مستخدم

### Stores
- `GET /api/stores` - قائمة المتاجر
- `GET /api/stores/{id}` - تفاصيل متجر
- `POST /api/stores` - إنشاء متجر
- `PUT /api/stores/{id}` - تحديث متجر

### Products
- `GET /api/products` - قائمة المنتجات
- `GET /api/products/{id}` - تفاصيل منتج
- `POST /api/products` - إنشاء منتج
- `PUT /api/products/{id}` - تحديث منتج

### Orders
- `GET /api/orders` - قائمة الطلبات
- `GET /api/orders/{id}` - تفاصيل طلب
- `POST /api/orders` - إنشاء طلب
- `PUT /api/orders/{id}/status` - تحديث حالة الطلب

## قاعدة البيانات 🗄️

النظام يحتوي على **20+ جدول** محسن للأداء مع:
- فهارس محسنة للاستعلامات السريعة
- قيود المرجعية (Foreign Keys) للحفاظ على سلامة البيانات
- Views محسنة للإحصائيات
- Triggers للعمليات التلقائية

### الجداول الرئيسية:
- `users` - المستخدمين
- `stores` - المتاجر
- `products` - المنتجات
- `orders` - الطلبات
- `order_details` - تفاصيل الطلبات
- `notifications` - الإشعارات
- `balances` - الأرصدة
- `reviews` - التقييمات

## الأمان 🔒

- **Password Hashing**: bcrypt مع salt
- **SQL Injection Protection**: Prepared Statements
- **XSS Protection**: Input Sanitization
- **CSRF Protection**: Token Validation
- **Rate Limiting**: API Request Limiting
- **File Upload Security**: Type & Size Validation
- **Session Security**: Secure Session Management

## الأداء ⚡

- **Database Indexing**: فهارس محسنة
- **Query Optimization**: استعلامات محسنة
- **Caching**: نظام تخزين مؤقت
- **Compression**: ضغط الاستجابات
- **CDN Ready**: جاهز للـ CDN

## التطوير 👨‍💻

### إعداد بيئة التطوير:
```bash
# تفعيل وضع التطوير
echo "APP_ENV=development" >> .env

# تفعيل عرض الأخطاء
echo "LOG_LEVEL=debug" >> .env
```

### إضافة ميزة جديدة:
1. إنشاء Model في `app/model/`
2. إنشاء Controller في `app/controller/`
3. إضافة Routes في `core/Router.php`
4. اختبار API endpoints

## النشر على الإنتاج 🚀

راجع ملف `config/hosting-setup.md` للتعليمات المفصلة

### خطوات سريعة:
1. رفع الملفات للخادم
2. إعداد قاعدة البيانات
3. تحديث `.env` للإنتاج
4. تفعيل HTTPS
5. إعداد النسخ الاحتياطي

## المساهمة 🤝

نرحب بالمساهمات! يرجى:
1. Fork المشروع
2. إنشاء branch للميزة الجديدة
3. Commit التغييرات
4. Push للـ branch
5. إنشاء Pull Request

## الدعم الفني 📞

- **البريد الإلكتروني**: <EMAIL>
- **الوثائق**: [docs.wardena.app](https://docs.wardena.app)
- **Issues**: [GitHub Issues](https://github.com/your-repo/wardena-api/issues)
- **المطور**: [Pearl Fibers](https://pearlfibers.com/)

## الترخيص 📄

هذا المشروع مرخص تحت [MIT License](LICENSE)

## الإصدارات 📋

- **v1.0.0** - الإصدار الأول
  - نظام إدارة المتاجر والمنتجات
  - نظام الطلبات والتوصيل
  - لوحة تحكم الأدمن
  - API متكامل

---

**تم تطويره بـ ❤️ لخدمة المجتمع العربي**
