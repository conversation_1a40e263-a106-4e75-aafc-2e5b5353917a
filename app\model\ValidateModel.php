<?php

class ValidateModel extends BaseModel
{

    protected $table;

    public function __construct($table)
    {
        parent::__construct();
        $this->table = $table;
    }

    public function elementExists($column, $x)
    {
        return $this->exists($column, $x);
    }

    public function formData($data)
    {
        $errors = [];

        $requiredFields = ['name', 'phone'];
        foreach ($requiredFields as $field) {
            if (empty($data[$field])) {
                $errors[$field] = $this->formatFieldName($field) . ' is required!';
            }
        }

        return $errors;
    }

    public function required($data, $required)
    {
        $errors = [];

        $requiredFields = $required;
        foreach ($requiredFields as $field) {
            if (empty($data[$field])) {
                $errors[$field] = $this->formatFieldName($field) . ' is required!';
            }
        }

        return $errors;
    }

    public function mobile($data)
    {
        $errors = [];

        if ($this->elementExists('phone', $data['phone'])) {
            $errors['phone'] = 'Mobile is already in use.';
        }

        return $errors;
    }

    
    public function username($data)
    {
        $errors = [];
        if ($this->elementExists('username', $data['username'])) {
            $errors['username'] = 'Username is already taken.';
        }
        return $errors;
    }

    public function email($data)
    {

        if (!filter_var($data['email'], FILTER_VALIDATE_EMAIL)) {
            $errors['email'] = 'Invalid email address!';
        } elseif ($this->elementExists('email', $data['email'])) {
            $errors['email'] = 'Email is already registered.';
        }

        return $errors;
    }

    public function password($data)
    {
        $errors = [];

        if ($data['password'] !== $data['confirm_password']) {
            $errors['confirm_password'] = 'Passwords do not match!';
        }

        // Password strength check
        if (strlen($data['password']) < 6) {
            $errors['password'] = 'Password must be at least 6 characters long!';
        }

        if (!preg_match('/[a-z]/', $data['password'])) {
            $errors['password'] = 'Password must contain at least one lowercase letter!';
        }

        if (!preg_match('/[A-Z]/', $data['password'])) {
            $errors['password'] = 'Password must contain at least one uppercase letter!';
        }

        if (!preg_match('/[0-9]/', $data['password'])) {
            $errors['password'] = 'Password must contain at least one number!';
        }

        if (!preg_match('/[!@#$%^&*()\-_=+{};:,<.>]/', $data['password'])) {
            $errors['password'] = 'Password must contain at least one special character!';
        }

        return $errors;
    }

    protected function formatFieldName($field)
    {
        $field = str_replace('_', ' ', $field);
        $field = ucwords($field);
        return $field;
    }
    
}
