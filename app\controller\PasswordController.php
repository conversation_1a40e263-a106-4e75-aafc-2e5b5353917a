<?php

class PasswordController extends BaseController {
    
    private $password;

    public function __construct($request, $response) {
        parent::__construct($request, $response);
        $this->password = new PasswordModel();
    }

    public function reset($token) {

        $user = $this->password->getUserByPasswordResetToken($token);

        if (!$user) {
            $data = [
                'title' => 'Password Reset',
                'msg' => 'Invalid token!',
            ];
            $this->render('../app/views/msg/fail.php', $data);
        }
        elseif (new DateTime() > new DateTime($user['expires_at'])) {
            $data = [
                'title' => 'Password Reset',
                'msg' => 'Token expired!',
            ];
            $this->render('../app/views/msg/fail.php', $data);
        }else{
            $data = [
                'title' => 'Password Reset 2',
                'token' => $token,
            ];
            $this->render('../app/views/login/set-password.php', $data);
        }
    }
    public function requestReset(){

        if ($this->isPost() && $this->getPost('email') !== null) {
            $email = $this->getPost('email');
            $user = $this->password->getUserByEmail($email);
        
            if (!$user) {
                $data = [
                    'title' => 'Password Reset',
                    'errors' => 'Invalid Email Address!',
                    'old' => $email,
                ];
                $this->render('../app/view/reset-password.php', $data);
                return;
            }
            
            $token = bin2hex(random_bytes(32));
            $expiryDate = (new DateTime())->modify('+1 hour')->format('Y-m-d H:i:s');
            
            $this->password->storePasswordResetToken($user['id'], $token, $expiryDate);
            
            $validation = $this->password->sendPasswordResetEmail($email, $token);
            if ($validation['success'] == true) {
                $data = [
                    'title' => 'Password Reset',
                    'msg' => $validation['errors'],
                ];
                $this->render('../app/views/msg/success.php', $data);
            }
            elseif ($validation['success'] == false) {
                $data = [
                    'title' => 'Password Reset',
                    'msg' => $validation['errors'],
                ];
                $this->render('../app/views/msg/fail.php', $data);
            }

        }
        else {
            $data = [
                'title' => 'Password Reset',
            ];
            $this->render('../app/view/reset-password.php', $data);
        }

    }

    public function set() {

        if ($this->isPost() && $this->getPost('token') !== null) {
            $fields = ['password', 'confirm_password', 'token'];
            $formData = $this->getFormData($fields);
            $validationErrors = $this->validateFormData($formData);

            if (!empty($validationErrors)) {
                $data = [
                    'title' => 'Password Reset',
                    'errors' => $validationErrors,
                    'token' => $formData['token'],
                ];
                $this->render('../app/views/login/set-password.php', $data);
                return;
            }

            $validation = $this->password->resetPassword($formData['token'], $formData['password']);

            if ($validation['success'] == true) {
                $data = [
                    'title' => 'Password Reset',
                    'msg' => $validation['errors'],
                ];
                $this->render('../app/views/msg/success.php', $data);
            }
            elseif ($validation['success'] == false) {
                $data = [
                    'title' => 'Password Reset',
                    'msg' => $validation['errors'],
                ];
                $this->render('../app/views/msg/fail.php', $data);
            }
        }
        else {
            $data = [
                'title' => 'Error-404',
                'msg' => "Sorry the page you're looking for is either not found or no longer available.",
            ];
            $this->render('../app/views/msg/wrong.php', $data);
        }
    }

    private function validateFormData($data) {
        $errors = [];

        $requiredFields = ['password', 'confirm_password'];
        foreach ($requiredFields as $field) {
            if (empty($data[$field])) {
                $errors[$field] = $this->formatFieldName($field) . ' is required!';
            }
        }

        if ($data['password'] !== $data['confirm_password']) {
            $errors['confirm_password'] = 'Passwords do not match!';
        }

        if (strlen($data['password']) < 8) {
            $errors['password'] = 'Password must be at least 8 characters long!';
        }

        if (!preg_match('/[a-z]/', $data['password'])) {
            $errors['password'] = 'Password must contain at least one lowercase letter!';
        }

        if (!preg_match('/[A-Z]/', $data['password'])) {
            $errors['password'] = 'Password must contain at least one uppercase letter!';
        }

        if (!preg_match('/[0-9]/', $data['password'])) {
            $errors['password'] = 'Password must contain at least one number!';
        }

        if (!preg_match('/[!@#$%^&*()\-_=+{};:,<.>]/', $data['password'])) {
            $errors['password'] = 'Password must contain at least one special character!';
        }

        if ($this->password->validatePassword($data) == true) {
            $errors['password'] = 'You can not use your old password';
        }

        return $errors;
    }

}
