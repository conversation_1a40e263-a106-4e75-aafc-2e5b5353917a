# استخدام PHP 8.0 مع Apache
FROM php:8.0-apache

# تثبيت الإضافات المطلوبة
RUN apt-get update && apt-get install -y \
    libpng-dev \
    libjpeg62-turbo-dev \
    libfreetype6-dev \
    libzip-dev \
    libicu-dev \
    libonig-dev \
    libxml2-dev \
    unzip \
    curl \
    && docker-php-ext-configure gd --with-freetype --with-jpeg \
    && docker-php-ext-install -j$(nproc) \
        pdo \
        pdo_mysql \
        mysqli \
        gd \
        zip \
        intl \
        mbstring \
        xml \
        json \
        fileinfo \
        opcache

# تفعيل mod_rewrite
RUN a2enmod rewrite headers

# إعداد Apache
COPY docker/apache/000-default.conf /etc/apache2/sites-available/000-default.conf
COPY docker/apache/apache2.conf /etc/apache2/apache2.conf

# إعداد PHP
COPY docker/php/php.ini /usr/local/etc/php/php.ini

# نسخ ملفات التطبيق
COPY . /var/www/html/

# إعداد الصلاحيات
RUN chown -R www-data:www-data /var/www/html \
    && chmod -R 755 /var/www/html \
    && chmod -R 777 /var/www/html/storage \
    && chmod -R 777 /var/www/html/public/uploads

# إنشاء ملف .env للـ Docker
RUN cp /var/www/html/.env.docker /var/www/html/.env || echo "No .env.docker found, using existing .env"

# تشغيل Apache
EXPOSE 80
CMD ["apache2-foreground"]
