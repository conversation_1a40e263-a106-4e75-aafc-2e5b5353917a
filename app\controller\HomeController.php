<?php

class HomeController extends BaseController
{
    private $model;
    private $auth;
    private $stores;
    private $slides;
    private $categories;
    private $products;
    private $productService;
    private $storeService;


    public function __construct($request, $response)
    {
        parent::__construct($request, $response);
        $this->model = new DashboardModel();
        $this->auth = new AuthService();
        $this->stores = new CrudModel('stores');
        $this->slides = new CrudModel('slides');
        $this->categories = new CrudModel('product_categories');
        $this->products = new CrudModel('products');
        $this->productService = new ProductService();
        $this->storeService = new StoreService();

    }

    public function index()
    {
        if ($this->auth->admin() == true) {
            $this->redirect('/dashboard');
        } elseif ($this->auth->owner() == true) {
            $this->redirect('/store');
        } elseif ($this->auth->employee() == true) {
            $this->redirect('/employee');
        } elseif ($this->auth->delivery() == true) {
            $this->redirect('/delivery');
        } else {
            $userId = $this->getSessionData('user_id');
            $user = $this->model->getUserData($userId);
            $categories = $this->categories->getElements();
            $products = $this->productService->getProducts();
            $slides = $this->slides->getElements();
            $stores = $this->storeService->getOrdered();

            $pagination = ['page' => 1, 'limit' => 30];
            $sorting = ['column' => 'id', 'order' => 'DESC'];
            $bestFilters['best_seller'] = ['operator' => '=', 'value' => '1'];
            $bestSeller = $this->productService->getFilteredProducts($bestFilters, $pagination, $sorting);
            $best = $bestSeller['products'];

            $topFilters['top_products'] = ['operator' => '=', 'value' => '1'];
            $topSeller = $this->productService->getFilteredProducts($topFilters, $pagination, $sorting);
            $top = $topSeller['products'];

            $data = [
                'title' => 'Home',
                'preloader' => true,
                'style' => null,
                'user' => $user,
                'categories' => $categories,
                'products' => $products,
                'slides' => $slides,
                'stores' => $stores,
                'best' => $best,
                'tops' => $top,
            ];

            $this->render('../app/view/visitor/home.php', $data);
        }
    }
    public function stores()
    {
        if ($this->auth->admin() == true) {
            $this->redirect('/dashboard');
        }
        if ($this->auth->owner() == true) {
            $this->redirect('/store');
        } else {
            $userId = $this->getSessionData('user_id');
            $user = $this->model->getUserData($userId);
            $result = $this->stores->getElements();
            $data = [
                'title' => 'Home',
                'preloader' => false,
                'style' => null,
                'user' => $user,
                'result' => $result
            ];

            $this->render('../app/view/visitor/stores.php', $data);
        }
    }
    public function categories()
    {
        if ($this->auth->admin() == true) {
            $this->redirect('/dashboard');
        }
        if ($this->auth->owner() == true) {
            $this->redirect('/store');
        } else {
            $userId = $this->getSessionData('user_id');
            $user = $this->model->getUserData($userId);
            $filters = [];
            $pagination = ['page' => $_GET['page'] ?? 1, 'limit' => 24];
            $sorting = ['column' => 'id', 'order' => 'ASC'];
            $filtered = $this->categories->getFiltered($filters, $pagination, $sorting);
            $result = $filtered['data'];

            $total = $filtered['total'];
            $totalPages = ceil($total / $pagination['limit']);

            $data = [
                'title' => 'Home',
                'preloader' => false,
                'style' => null,
                'user' => $user,
                'result' => $result,
                'totalPages' => $totalPages,
                'currentPage' => $pagination['page']

            ];

            $this->render('../app/view/visitor/categories.php', $data);
        }
    }
    public function products()
    {
        if ($this->auth->admin() == true) {
            $this->redirect('/dashboard');
        }
        if ($this->auth->owner() == true) {
            $this->redirect('/store');
        } else {
    
            $userId = $this->getSessionData('user_id');
            $user = $this->model->getUserData($userId);
    
            // Sanitize and handle search query
            $searchQuery = isset($_GET['search']) ? htmlspecialchars(trim($_GET['search'])) : '';
    
            // Initialize filters
            $filters = [];
    
            if (!empty($_GET['category_id'])) {
                $filters['category_id'] = (int)$_GET['category_id'];
            }
    
            if (!empty($searchQuery)) {
                $filters['name'] = ['operator' => 'like', 'value' => '%' . $searchQuery . '%'];
            }
    
            // Price range filter
            if (!empty($_GET['min_price']) && !empty($_GET['max_price'])) {
                $filters['price'] = ['operator' => 'between', 'value' => [(float)$_GET['min_price'], (float)$_GET['max_price']]];
            }
    
            // Pagination handling
            $page = isset($_GET['page']) ? max(1, (int)$_GET['page']) : 1;
            $limit = 24;
            $pagination = ['page' => $page, 'limit' => $limit];
    
            // Sorting options
            $sortOption = $_GET['sort'] ?? 'price_asc';
            switch ($sortOption) {
                case 'price_asc':
                    $sorting = ['column' => 'price', 'order' => 'ASC'];
                    break;
                case 'price_desc':
                    $sorting = ['column' => 'price', 'order' => 'DESC'];
                    break;
                case 'name_asc':
                    $sorting = ['column' => 'name', 'order' => 'ASC'];
                    break;
                case 'name_desc':
                    $sorting = ['column' => 'name', 'order' => 'DESC'];
                    break;
                default:
                    $sorting = ['column' => 'price', 'order' => 'ASC'];
            }
    
            // Fetch filtered products
            $filtered = $this->productService->getFilteredProducts($filters, $pagination, $sorting);
            $result = $filtered['products'];
            $total = $filtered['total'];
            $totalPages = ceil($total / $pagination['limit']);
    
            // Fetch and process categories
            $categories = $this->categories->getElements();
            $hierarchicalCategories = $this->buildHierarchy($categories);
            $flatCategories = $this->flattenHierarchy($hierarchicalCategories);
    
            // Prepare data for the view
            $data = [
                'title' => 'Home',
                'preloader' => false,
                'style' => null,
                'user' => $user,
                'result' => $result,
                'totalPages' => $totalPages,
                'categories' => $flatCategories,
                'currentPage' => $pagination['page'],
                'searchQuery' => $searchQuery,
                'selectedSort' => $sortOption,
            ];
    
            // Render the products page for visitors
            $this->render('../app/view/visitor/products.php', $data);
        }
    }
    
    public function storeProducts()
    {
        if ($this->auth->admin() == true) {
            $this->redirect('/dashboard');
        }
        if ($this->auth->owner() == true) {
            $this->redirect('/store');
        } else {

            $userId = $this->getSessionData('user_id');
            $user = $this->model->getUserData($userId);

            $searchQuery = isset($_GET['search']) ? htmlspecialchars(trim($_GET['search'])) : '';

            $filters = [];
    
            if (!empty($_GET['category_id'])) {
                $filters['category_id'] = (int)$_GET['category_id'];
            }
            if (!empty($_GET['store'])) {
                $filters['store_id'] = (int)$_GET['store'];
            }
            if (!empty($searchQuery)) {
                $filters['name'] = ['operator' => 'like', 'value' => '%' . $searchQuery . '%'];
            }
            if (!empty($_GET['min_price']) && !empty($_GET['max_price'])) {
                $filters['price'] = ['operator' => 'between', 'value' => [(float)$_GET['min_price'], (float)$_GET['max_price']]];
            }

            $pagination = ['page' => $_GET['page'] ?? 1, 'limit' => 24];

            $sortOption = $_GET['sort'] ?? null;
            switch ($sortOption) {
                case 'price_asc':
                    $sorting = ['column' => 'price', 'order' => 'ASC'];
                    break;
                case 'price_desc':
                    $sorting = ['column' => 'price', 'order' => 'DESC'];
                    break;
                case 'name_asc':
                    $sorting = ['column' => 'name', 'order' => 'ASC'];
                    break;
                case 'name_desc':
                    $sorting = ['column' => 'name', 'order' => 'DESC'];
                    break;
                default:
                    $sorting = ['column' => 'price', 'order' => 'ASC'];
            }

            $filtered = $this->productService->getFilteredProducts($filters, $pagination, $sorting);
            $result = $filtered['products'];

            $total = $filtered['total'];
            $totalPages = ceil($total / $pagination['limit']);

            $categories = $this->categories->getElements();
            
            $hierarchicalCategories = $this->buildHierarchy($categories);
            $flatCategories = $this->flattenHierarchy($hierarchicalCategories);

            $data = [
                'title' => 'Home',
                'preloader' => false,
                'style' => null,
                'user' => $user,
                'result' => $result,
                'totalPages' => $totalPages,
                'categories' => $flatCategories,
                'currentPage' => $pagination['page']
            ];

            $this->render('../app/view/visitor/store-products.php', $data);
        }
    }
    public function thankYou()
    {
        if ($this->auth->admin() == true) {
            $this->redirect('/dashboard');
        }
        if ($this->auth->owner() == true) {
            $this->redirect('/store');
        } else {
            $userId = $this->getSessionData('user_id');
            $user = $this->model->getUserData($userId);
            $data = [
                'title' => 'Home',
                'preloader' => false,
                'style' => null,
                'user' => $user,
            ];

            $this->render('../app/view/visitor/thank-you.php', $data);
        }
    }
    public function contact()
    {
        if ($this->auth->admin() == true) {
            $this->redirect('/dashboard');
        }
        if ($this->auth->owner() == true) {
            $this->redirect('/store');
        } else {
            $userId = $this->getSessionData('user_id');
            $user = $this->model->getUserData($userId);
            $data = [
                'title' => 'Home',
                'preloader' => false,
                'style' => null,
                'user' => $user,
            ];

            $this->render('../app/view/visitor/contact.php', $data);
        }
    }
}
