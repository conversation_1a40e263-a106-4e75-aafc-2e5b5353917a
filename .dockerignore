# Git
.git
.gitignore
.gitattributes

# Documentation
README.md
INSTALLATION.md
deployment-checklist.md
*.md

# Development files
.vscode/
.idea/
*.log
*.tmp
*.temp

# OS files
.DS_Store
Thumbs.db
desktop.ini

# Node modules (if any)
node_modules/
npm-debug.log*

# Composer
vendor/composer/installed.json

# Environment files (will be copied separately)
.env.example
.env.local
.env.production

# Cache and temporary files
storage/cache/*
storage/logs/*
!storage/cache/.gitkeep
!storage/logs/.gitkeep

# Uploads (will be mounted as volume)
public/uploads/*
!public/uploads/.gitkeep

# Docker files
docker-compose.yml
docker-compose.override.yml
Dockerfile
.dockerignore

# Scripts
start.sh
start.bat
stop.sh
stop.bat

# Test files
test-setup.php
debug.php
quick-setup.php

# Backup files
*.bak
*.backup
*.sql.gz
