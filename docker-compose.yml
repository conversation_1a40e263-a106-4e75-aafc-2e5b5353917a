version: '3.8'

services:
  # PHP Apache Web Server
  web:
    build: .
    container_name: wardena-web
    ports:
      - "8080:80"
    volumes:
      - .:/var/www/html
      - ./storage:/var/www/html/storage
      - ./public/uploads:/var/www/html/public/uploads
    environment:
      - DB_HOST=db
      - DB_NAME=wardena_db
      - DB_USER=wardena
      - DB_PASS=wardena123
      - APP_ENV=development
      - URL=http://localhost:8080/
      - API_URL=http://localhost:8080/api/
    depends_on:
      db:
        condition: service_healthy
    networks:
      - wardena-network

  # MySQL Database
  db:
    image: mysql:8.0
    container_name: wardena-db
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: root123
      MYSQL_DATABASE: wardena_db
      MYSQL_USER: wardena
      MYSQL_PASSWORD: wardena123
      MYSQL_CHARSET: utf8mb4
      MYSQL_COLLATION: utf8mb4_unicode_ci
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./config/general.sql:/docker-entrypoint-initdb.d/01-schema.sql
      - ./docker/mysql/init.sql:/docker-entrypoint-initdb.d/02-init.sql
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "wardena", "-pwardena123"]
      timeout: 20s
      retries: 10
    networks:
      - wardena-network

  # phpMyAdmin (اختياري)
  phpmyadmin:
    image: phpmyadmin/phpmyadmin
    container_name: wardena-phpmyadmin
    restart: always
    ports:
      - "8081:80"
    environment:
      PMA_HOST: db
      PMA_USER: wardena
      PMA_PASSWORD: wardena123
      MYSQL_ROOT_PASSWORD: root123
    depends_on:
      - db
    networks:
      - wardena-network

  # Redis Cache (اختياري)
  redis:
    image: redis:7-alpine
    container_name: wardena-redis
    restart: always
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - wardena-network

volumes:
  mysql_data:
    driver: local
  redis_data:
    driver: local

networks:
  wardena-network:
    driver: bridge
