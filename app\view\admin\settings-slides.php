<!--start content-->
<main class="page-content">
    <!--breadcrumb-->
    <div class="page-breadcrumb d-none d-sm-flex align-items-center mb-3">
        <a class="breadcrumb-title pe-3" href="/settings">الاعدادات</a>
        <div class="ps-3">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb mb-0 p-0">
                    <li class="breadcrumb-item"><a href="javascript:;"><i class="bx bx-home-alt"></i></a>
                    </li>
                    <li class="breadcrumb-item active" aria-current="page">اعدادات المتجر</li>
                </ol>
            </nav>
        </div>
    </div>
    <!--end breadcrumb-->
    <div class="card">
        <div class="card-header py-3">
            <h6 class="mb-0">الشرائح</h6>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-12 col-lg-4 d-flex">
                    <div class="card border shadow-none w-100">
                        <div class="card-body">
                            <form class="row g-3" method="post" action="/slides/new" enctype="multipart/form-data">
                                <div class="col-12">
                                    <label class="form-label">الموضوع</label>
                                    <input type="text" name="title" class="form-control" placeholder="موضوع الشريحة">
                                </div>
                                <div class="col-12">
                                    <label class="form-label">الرابط</label>
                                    <input type="text" name="url" class="form-control" placeholder="/url" value="">
                                </div>
                                <div class="col-12">
                                    <label class="form-label">العنوان</label>
                                    <input type="text" name="heading" class="form-control" placeholder="العنوان الرئيسي">
                                </div>
                                <div class="col-12">
                                    <label class="form-label">النص</label>
                                    <textarea type="number" name="text" class="form-control">نص الشريحة</textarea>
                                </div>
                                <div class="col-12">
                                    <div class="input-group mb-3">
                                        <label class="input-group-text" for="inputGroupFile01">الصورة</label>
                                        <input type="file" name="file" class="form-control" id="inputGroupFile01">
                                    </div>
                                </div>
                                
                                <div class="col-12">
                                    <div class="d-grid">
                                        <button type="submit" class="btn btn-primary">اضافة</button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
                <div class="col-12 col-lg-8 d-flex">
                    <div class="card border shadow-none w-100">
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table align-middle">
                                    <thead class="table-light">
                                        <tr>
                                            <th>الرقم</th>
                                            <th>الموضوع</th>
                                            <th>العنوان</th>
                                            <th>النص</th>
                                            <th>الرابط</th>
                                            <th>الخيارات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($result as $slide) { ?>

                                            <tr>
                                                <td>#<?php echo $slide['id'] ?></td>
                                                <td><?php echo $slide['title'] ?></td>
                                                <td>
                                                    <a class="d-flex align-items-center gap-2" href="#">
                                                        <div class="product-box">
                                                            <img src="/uploads/slides/<?php echo $slide['image'] ?>" alt="">
                                                        </div>
                                                        <div>
                                                            <h6 class="mb-0 product-title"><?php echo $slide['heading'] ?></h6>
                                                        </div>
                                                    </a>
                                                </td>
                                                <td><?php echo $slide['text'] ?></td>
                                                <td><?php echo $slide['url'] ?></td>
                                                <td>
                                                    <div class="d-flex align-items-center gap-3 fs-6">
                                                        <a href="/slides/edit?id=<?php echo $slide['id'] ?>" class="text-primary" data-bs-toggle="modal" data-bs-target="#modal-<?php echo $slide['id'] ?>" data-bs-placement="bottom" title="" data-bs-original-title="Edit info" aria-label="Edit"><i class="bi bi-pencil-fill"></i></a>
                                                        <a href="/slides/delete?id=<?php echo $slide['id'] ?>" class="text-danger" data-bs-toggle="tooltip" data-bs-placement="bottom" title="" data-bs-original-title="Delete" aria-label="Delete"><i class="bi bi-trash-fill"></i></a>
                                                    </div>
                                                </td>
                                            </tr>
                                            <!-- Modal -->
                                            <div class="modal fade" id="modal-<?php echo $slide['id'] ?>" tabindex="-1" aria-hidden="true">
                                                <div class="modal-dialog modal-lg modal-dialog-centered">
                                                    <div class="modal-content bg-primary">
                                                        <div class="modal-header">
                                                            <h5 class="modal-title text-white">تعديل الشريحة</h5>
                                                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                                        </div>
                                                        <div class="modal-body text-white">
                                                            <div class="card-body">
                                                                <form class="row g-3" method="post" action="/slides/update?id=<?php echo $rate['id'] ?>" enctype="multipart/form-data">
                                                                    <div class="col-12">
                                                                        <label class="form-label">الموضوع</label>
                                                                        <input type="text" name="title" class="form-control" value="<?php echo $slide['title'] ?>">
                                                                    </div>
                                                                    <div class="col-12">
                                                                        <label class="form-label">الرابط</label>
                                                                        <input type="text" name="url" class="form-control" value="<?php echo $slide['url'] ?>">
                                                                    </div>
                                                                    <div class="col-12">
                                                                        <label class="form-label">العنوان</label>
                                                                        <input type="text" name="heading" class="form-control" value="<?php echo $slide['heading'] ?>">
                                                                    </div>
                                                                    <div class="col-12">
                                                                        <label class="form-label">النص</label>
                                                                        <textarea type="number" name="text" class="form-control"><?php echo $slide['text'] ?></textarea>
                                                                    </div>
                                                                    <div class="col-12 m-4">
                                                                        <img src="/uploads/slides/<?php echo $slide['image'] ?>" class="border" height="50" alt="">
                                                                    </div>
                                                                    <div class="col-12">
                                                                        <div class="input-group mb-3 mt-3">
                                                                            <label class="input-group-text" for="inputGroupFile01">الصورة</label>
                                                                            <input type="file" name="file" class="form-control" id="inputGroupFile01">
                                                                        </div>
                                                                    </div>
                                                                    <div class="col-12">
                                                                        <div class="d-flex modal-footer">
                                                                            <button type="submit" class="btn btn-primary" data-bs-dismiss="modal">تعديل</button>
                                                                            <button class="btn btn-primary" data-bs-dismiss="modal">اغلاق</button>
                                                                        </div>
                                                                    </div>
                                                                </form>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                        <?php } ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div><!--end row-->
        </div>
    </div>

</main>
<!--end page main-->
<script>
    document.addEventListener("DOMContentLoaded", function() {
        const selectElement = document.getElementById("ourFee");

        selectElement.addEventListener("change", function() {
            const selectedFee = this.value;

            fetch("/settings/updateOurFee", {
                method: "POST",
                headers: {
                    "Content-Type": "application/x-www-form-urlencoded",
                },
                body: `fee=${encodeURIComponent(selectedFee)}`,
                })
                .then(response => {
                    if (!response.ok) {
                        throw new Error("Failed to update fee");
                    }
                    return response.json();
                })
                .then(data => {
                    console.log("Fee updated successfully:", data.message);
                })
                .catch(error => {
                    console.error("Error updating fee:", error);
                });
        });
    });
</script>