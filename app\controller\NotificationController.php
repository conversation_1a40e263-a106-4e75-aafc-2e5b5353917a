<?php

class NotificationController extends BaseController {

    private $model;

    public function __construct()
    {
        $this->model = new NotificationModel();
    }

    // Display notifications for logged-in user
    public function index() {
        $user_id = $this->getSessionData('user_id');
        $notifications = $this->model->getUserNotifications($user_id);
        header('Content-Type: application/json');
        echo json_encode($notifications);
    }

    // Mark a notification as read for a specific user
    public function markAsRead($id) {
        $user_id = $this->getSessionData('user_id');
        header('Content-Type: application/json');
        if ($this->model->markAsRead($id, $user_id)) {
            echo json_encode(['success' => true]);
        } else {
            echo json_encode(['success' => false]);
        }
    }

    // Notify all admins about a new order
    public function notifyAdminsAboutOrder($order_id) {
        $title = "New Order Received";
        $message = "A new order (ID: $order_id) has been placed.";
        $url = URLROOT . "/orders/view/$order_id";

        $this->model->sendNotificationToRole('admin', $title, $message, 'success', $url);
        redirect('orders/index');
    }

}