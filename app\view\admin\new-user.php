<!--start content-->

<main class="page-content">
    <div class="card">
        <div class="card-body p-4 p-sm-5">
            <h5 class="card-title">اضافة مستخدم جديد</h5>
            <form class="form-body" method="POST" action="/users/new-user">
                <div class="login-separater text-center mb-4"> <span>تسجيل <?php echo $title; ?></span>
                    <hr>
                </div>
                <div class="row g-3">
                    <div class="col-12 col-lg-6">
                        <label for="inputName" class="form-label">الاسم</label>
                        <div class="ms-auto position-relative">
                            <div class="position-absolute top-50 translate-middle-y search-icon px-3"><i class="bi bi-person-circle"></i></div>
                            <input type="text" name="name" class="form-control radius-30 ps-5 <?php echo !empty($errors['name']) ? 'is-invalid' : ''; ?>" id="inputName" placeholder="ادخل الاسم الاول" value="<?php echo htmlspecialchars($old['name'] ?? ''); ?>">
                        </div>
                        <?php if (!empty($errors['name'])) : ?> <div class="text-danger"> <?php echo htmlspecialchars($errors['name']); ?> </div> <?php endif; ?>

                    </div>
                    <div class="col-12 col-lg-6">
                        <label for="inputName" class="form-label">اللقب</label>
                        <div class="ms-auto position-relative">
                            <div class="position-absolute top-50 translate-middle-y search-icon px-3"><i class="bi bi-person-circle"></i></div>
                            <input type="text" name="last_name" class="form-control radius-30 ps-5 <?php echo !empty($errors['last_name']) ? 'is-invalid' : ''; ?>" id="inputName" placeholder="ادخل اللقب" value="<?php echo htmlspecialchars($old['last_name'] ?? ''); ?>">
                        </div>
                        <?php if (!empty($errors['last_name'])) : ?> <div class="text-danger"> <?php echo htmlspecialchars($errors['last_name']); ?> </div> <?php endif; ?>

                    </div>
                    <div class="col-12 col-lg-6">
                        <label for="inputEmailAddress" class="form-label">عنوان البريد الالكتروني</label>
                        <div class="ms-auto position-relative">
                            <div class="position-absolute top-50 translate-middle-y search-icon px-3"><i class="bi bi-envelope-fill"></i></div>
                            <input type="email" name="email" class="form-control radius-30 ps-5 <?php echo !empty($errors['email']) ? 'is-invalid' : ''; ?>" id="inputEmailAddress" placeholder="Email Address" value="<?php echo htmlspecialchars($old['email'] ?? ''); ?>">
                        </div>
                        <?php if (!empty($errors['email'])) : ?> <div class="text-danger"> <?php echo htmlspecialchars($errors['email']); ?> </div> <?php endif; ?>

                    </div>
                    <div class="col-12 col-lg-6">
                        <label for="inputEmailAddress" class="form-label">رقم المحمول</label>
                        <div class="ms-auto position-relative">
                            <div class="position-absolute top-50 translate-middle-y search-icon px-3"><i class="bi bi-envelope-fill"></i></div>
                            <input type="tel" name="phone" class="form-control radius-30 ps-5 <?php echo !empty($errors['phone']) ? 'is-invalid' : ''; ?>" id="inputEmailAddress" placeholder="************" value="<?php echo htmlspecialchars($old['phone'] ?? ''); ?>">
                        </div>
                        <?php if (!empty($errors['phone'])) : ?> <div class="text-danger"> <?php echo htmlspecialchars($errors['phone']); ?> </div> <?php endif; ?>

                    </div>
                    <div class="col-12">
                        <label for="inputName" class="form-label">رتبة المستخدم</label>
                        <div class="ms-auto position-relative">
                            <div class="position-absolute top-50 translate-middle-y search-icon px-3"><i class="bi bi-person-circle"></i></div>
                            <select name="role" class="form-control radius-30 ps-5" id="inputName">
                                <option value="<?php echo $role; ?>"><?php echo $role; ?></option>
                            </select>
                        </div>
                    </div>
                    <div class="login-separater text-center mb-4"> <span>معلومات العنوان</span>
                        <hr>
                    </div>
                    <div class="col-12 col-lg-6">
                        <label for="inputName" class="form-label">الدولة</label>
                        <div class="ms-auto position-relative">
                            <div class="position-absolute top-50 translate-middle-y search-icon px-3"><i class="bi bi-person-circle"></i></div>
                            <input type="text" name="country" class="form-control radius-30 ps-5 <?php echo !empty($errors['country']) ? 'is-invalid' : ''; ?>" id="inputName" placeholder="Enter Name" value="العراق">
                        </div>
                        <?php if (!empty($errors['country'])) : ?> <div class="text-danger"> <?php echo htmlspecialchars($errors['country']); ?> </div> <?php endif; ?>

                    </div>
                    <div class="col-12 col-lg-6">
                        <label for="inputName" class="form-label">المحافظة</label>
                        <div class="ms-auto position-relative">
                            <div class="position-absolute top-50 translate-middle-y search-icon px-3"><i class="bi bi-person-circle"></i></div>
                            <input type="text" name="state" class="form-control radius-30 ps-5 <?php echo !empty($errors['state']) ? 'is-invalid' : ''; ?>" id="inputName" placeholder="ادخل اسم المحافظة" value="<?php echo htmlspecialchars($old['state'] ?? ''); ?>">
                        </div>
                        <?php if (!empty($errors['state'])) : ?> <div class="text-danger"> <?php echo htmlspecialchars($errors['state']); ?> </div> <?php endif; ?>

                    </div>
                    <div class="col-12 col-lg-6">
                        <label for="inputName" class="form-label">المدينة</label>
                        <div class="ms-auto position-relative">
                            <div class="position-absolute top-50 translate-middle-y search-icon px-3"><i class="bi bi-person-circle"></i></div>
                            <input type="text" name="city" class="form-control radius-30 ps-5 <?php echo !empty($errors['city']) ? 'is-invalid' : ''; ?>" id="inputName" placeholder="ادخل اسم النطقة" value="<?php echo htmlspecialchars($old['city'] ?? ''); ?>">
                        </div>
                        <?php if (!empty($errors['city'])) : ?> <div class="text-danger"> <?php echo htmlspecialchars($errors['city']); ?> </div> <?php endif; ?>

                    </div>
                    <div class="col-12 col-lg-6">
                        <label for="inputName" class="form-label">عنوان الشارع</label>
                        <div class="ms-auto position-relative">
                            <div class="position-absolute top-50 translate-middle-y search-icon px-3"><i class="bi bi-person-circle"></i></div>
                            <input type="text" name="address" class="form-control radius-30 ps-5 <?php echo !empty($errors['address']) ? 'is-invalid' : ''; ?>" id="inputName" placeholder="محلة\زقاق\دار" value="<?php echo htmlspecialchars($old['address'] ?? ''); ?>">
                        </div>
                        <?php if (!empty($errors['address'])) : ?> <div class="text-danger"> <?php echo htmlspecialchars($errors['address']); ?> </div> <?php endif; ?>

                    </div>
                    <div class="login-separater text-center mb-4"> <span>اسم المستخدم والرمز السري</span>
                        <hr>
                    </div>
                    <div class="col-12">
                        <label for="inputName" class="form-label">اسم المستخدم</label>
                        <div class="ms-auto position-relative">
                            <div class="position-absolute top-50 translate-middle-y search-icon px-3"><i class="bi bi-person-circle"></i></div>
                            <input type="text" name="username" class="form-control radius-30 ps-5 <?php echo !empty($errors['username']) ? 'is-invalid' : ''; ?>" id="inputName" placeholder="ادخل اسم المستخدم" value="<?php echo htmlspecialchars($old['username'] ?? ''); ?>">
                        </div>
                        <?php if (!empty($errors['username'])) : ?> <div class="text-danger"> <?php echo htmlspecialchars($errors['username']); ?> </div> <?php endif; ?>

                    </div>
                    <div class="col-12 col-lg-6">
                        <label for="inputChoosePassword" class="form-label">الرمز السري</label>
                        <div class="ms-auto position-relative">
                            <div class="position-absolute top-50 translate-middle-y search-icon px-3"><i class="bi bi-lock-fill"></i></div>
                            <input type="password" name="password" class="form-control radius-30 ps-5 <?php echo !empty($errors['password']) ? 'is-invalid' : ''; ?>" id="inputChoosePassword" placeholder="*****">
                        </div>
                        <?php if (!empty($errors['password'])) : ?> <div class="text-danger"> <?php echo htmlspecialchars($errors['password']); ?> </div> <?php endif; ?>

                    </div>
                    <div class="col-12 col-lg-6">
                        <label for="inputChoosePassword" class="form-label">تأكيد الرمز</label>
                        <div class="ms-auto position-relative">
                            <div class="position-absolute top-50 translate-middle-y search-icon px-3"><i class="bi bi-lock-fill"></i></div>
                            <input type="password" name="confirm_password" class="form-control radius-30 ps-5 <?php echo !empty($errors['confirm_password']) ? 'is-invalid' : ''; ?>" id="inputChoosePassword" placeholder="*****">
                        </div>
                        <?php if (!empty($errors['confirm_password'])) : ?> <div class="text-danger"> <?php echo htmlspecialchars($errors['confirm_password']); ?> </div> <?php endif; ?>

                    </div>
                    <div class="col-12 mt-5">
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary radius-30">تسجيل</button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</main>

<!--end page main-->