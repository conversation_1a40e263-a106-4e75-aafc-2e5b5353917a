<?php

class LoginModel
{

    private $db;

    public function __construct()
    {
        $this->db = Database::getInstance()->connect();
    }

    public function validateUser($data) {
        $errors = [];
        $requiredFields = ['email', 'password'];
        foreach ($requiredFields as $field) {
            if (empty($data[$field])) {
                $errors[$field] = ucwords($field) . ' is required!';
            }
        }
    
        // Check if username is in email format
        if (!filter_var($data['email'], FILTER_VALIDATE_EMAIL)) {
            // If not an email, check if it's a valid username (adjust regex as needed)
            if (!preg_match('/^[a-zA-Z0-9_]{3,}$/', $data['email'])) {
                $errors['email'] = 'Invalid username or email format';
                return ['success' => false, 'errors' => $errors];
            }
        }
    
        $stmt = $this->db->prepare("SELECT password FROM users WHERE username = :username OR email = :email");
        $stmt->bindParam(':username', $data['email']);
        $stmt->bindParam(':email', $data['email']);
        $stmt->execute();
        $row = $stmt->fetch(PDO::FETCH_ASSOC);
    
        if (!$row) {
            $errors['email'] = 'User not found';
            return ['success' => false, 'errors' => $errors];
        }
    
        if (!password_verify($data['password'], $row['password'])) {
            $errors['password'] = 'Incorrect password';
            return ['success' => false, 'errors' => $errors];
        }
    
        return ['success' => true];
    }

    public function getUserByUsername($username)
    {
        $stmt = $this->db->prepare("SELECT * FROM users WHERE username = :username OR email = :email");

        $stmt->bindParam(':username', $username);
        $stmt->bindParam(':email', $username);

        $stmt->execute();

        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    //remember me functionallity

    public function getUserById($userId)
    {
        $stmt = $this->db->prepare("SELECT * FROM users WHERE id = :username");
        $stmt->bindParam(':username', $userId);
        $stmt->execute();
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    public function storeRememberToken($userId, $hashedToken, $expiresAt) {
        $stmt = $this->db->prepare("INSERT INTO user_remember_tokens (user_id, token, expires_at) VALUES (:user_id, :token, :expires_at)");
    
        $stmt->bindParam(':user_id', $userId);
        $stmt->bindParam(':token', $hashedToken);
        $stmt->bindParam(':expires_at', $expiresAt);
    
        return $stmt->execute();
    }

    public function getRememberToken($userId) {
        $stmt = $this->db->prepare("SELECT token FROM user_remember_tokens WHERE user_id = :user_id");
        $stmt->bindParam(':user_id', $userId);
        $stmt->execute();
    
        return $stmt->fetchColumn();
    }

    public function deleteRememberToken($userId) {
        $stmt = $this->db->prepare("DELETE FROM user_remember_tokens WHERE user_id = :user_id");
        $stmt->bindParam(':user_id', $userId);
        
        return $stmt->execute();
    }

}
