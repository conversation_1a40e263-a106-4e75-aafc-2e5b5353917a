<?php

class ProductsController extends BaseController
{

    private $model;
    private $auth;
    private $service;
    private $categories;
    private $brands;
    private $stores;
    private $users;
    private $val;


    public function __construct($request, $response)
    {
        parent::__construct($request, $response);
        $this->model = new CrudModel('products');
        $this->service = new ProductService();
        $this->auth = new AuthService();
        $this->categories = new CrudModel('product_categories');
        $this->brands = new CrudModel('brands');
        $this->stores = new CrudModel('stores');
        $this->users = new CrudModel('users');
        $this->val = new ValidateModel('products');
    }

    public function index()
    {
        if ($this->auth->admin() == true || $this->auth->employee() == true) {

            $userId = $this->getSessionData('user_id');
            $user = $this->users->getElement($userId);

            // Sanitize search query input
            $searchQuery = isset($_GET['search']) ? htmlspecialchars(trim($_GET['search'])) : '';
            
            // Collect filters
            $filters = [];

            if (!empty($_GET['category_id'])) {
                $filters['category_id'] = (int) $_GET['category_id']; // Cast to integer to prevent SQL injection
            }

            if (!empty($searchQuery)) {
                $filters['name'] = ['operator' => 'like', 'value' => $searchQuery];
            }

            // Additional filtering options (e.g., price range)
            if (!empty($_GET['min_price']) && !empty($_GET['max_price'])) {
                $filters['price'] = ['operator' => 'between', 'value' => [(float) $_GET['min_price'], (float) $_GET['max_price']]];
            }

            // Pagination
            $page = isset($_GET['page']) ? max(1, (int)$_GET['page']) : 1;
            $limit = 25;
            $pagination = ['page' => $page, 'limit' => $limit];

            // Sorting
            $sortOption = $_GET['sort'] ?? 'price_asc';
            switch ($sortOption) {
                case 'price_asc':
                    $sorting = ['column' => 'price', 'order' => 'ASC'];
                    break;
                case 'price_desc':
                    $sorting = ['column' => 'price', 'order' => 'DESC'];
                    break;
                case 'name_asc':
                    $sorting = ['column' => 'name', 'order' => 'ASC'];
                    break;
                case 'name_desc':
                    $sorting = ['column' => 'name', 'order' => 'DESC'];
                    break;
                default:
                    $sorting = ['column' => 'price', 'order' => 'ASC'];
            }

            // Fetch filtered products
            $filtered = $this->service->getFilteredProducts($filters, $pagination, $sorting);
            $result = $filtered['products'];
            $total = $filtered['total'];
            $totalPages = ceil($total / $pagination['limit']);

            // Fetch and process categories
            $categories = $this->categories->getElements();
            $hierarchicalCategories = $this->buildHierarchy($categories);
            $flatCategories = $this->flattenHierarchy($hierarchicalCategories);

            // Prepare data for the view
            $data = [
                'title' => 'Products',
                'style' => null,
                'user' => $user,
                'result' => $result,
                'totalPages' => $totalPages,
                'categories' => $flatCategories,
                'currentPage' => $pagination['page'],
                'searchQuery' => $searchQuery,
                'selectedSort' => $sortOption,
            ];

            // Render the admin products view with search results
            $this->render('../app/view/admin/products.php', $data);

        } else {
            $this->redirect('/login');
        }
    }


    public function newProduct()
    {
        if ($this->isLoggedIn()) {
            if ($this->isPost()) {
                $this->handelNewProduct();
            }
            $userId = $this->getSessionData('user_id');
            $user = $this->users->getElement($userId);
            $stores = $this->stores->getElements();
            $brands = $this->brands->getElements();
            $categories = $this->categories->getElements();

            $hierarchicalCategories = $this->buildHierarchy($categories);
            $flatCategories = $this->flattenHierarchy($hierarchicalCategories);

            $data = [
                'title' => 'Products',
                'style' => null,
                'user' => $user,
                'stores' => $stores,
                'brands' => $brands,
                'categories' => $flatCategories
            ];

            $this->render('../app/view/admin/new-product.php', $data);
        } else {
            $this->redirect('/login');
        }
    }

    public function edit()
    {
        if ($this->isLoggedIn()) {
            if ($this->isPost()) {
                $this->updateProduct();
            } else {
                $userId = $this->getSessionData('user_id');
                $user = $this->users->getElement($userId);
                $id = $_GET['id'];
                $product = $this->model->getElement($id);
                $stores = $this->stores->getElements();
                $brands = $this->brands->getElements();
                $categories = $this->categories->getElements();

                $hierarchicalCategories = $this->buildHierarchy($categories);
                $flatCategories = $this->flattenHierarchy($hierarchicalCategories);

                $data = [
                    'title' => 'Products',
                    'style' => null,
                    'user' => $user,
                    'stores' => $stores,
                    'brands' => $brands,
                    'categories' => $flatCategories,
                    'product' => $product
                ];

                $this->render('../app/view/admin/edit-product.php', $data);
            }
        } else {
            $this->redirect('/login');
        }
    }

    public function updateProduct()
    {
        try {

            $this->model->beginTransaction();

            $fields = ['store_id', 'category_id', 'brand_id', 'name', 'description', 'price', 'app_percentage'];
            $idField = ['id'];
            $idData = $this->getFormData($idField);

            $formData = $this->getFormData($fields);
            $id = $idData['id'];

            $required = ['store_id', 'category_id', 'brand_id', 'name', 'price', 'app_percentage'];
            $validationErrors = $this->val->required($formData, $required);

            if (!empty($validationErrors)) {
                $data = [
                    'errors' => $validationErrors,
                    'old' => $formData,
                ];
                $this->redirect('/products/edit?id='.$id, $data);
                $this->logError('Failed to validate');

                return;
            }

            if (!$this->model->updateElement($formData, $id)) {
                $this->logError('Failed to create store');
                $this->model->rollBack();
            } else {
                $this->model->commit();
                $with = ['response' => 'success'];
                $this->redirect('/products/edit?id='.$id, $with);
                return;
            }
        } catch (Exception $e) {
            $this->model->rollBack();
            $this->logError($e->getMessage());
            return $this->redirect('/products/edit?id='.$id);
        }
    }

    public function updateProductImg() 
    {

    try {

        $this->model->beginTransaction();
        $fields = ['store_id', 'id'];
        $formData = $this->getFormData($fields);
        $id = $formData['id'];

        $file = $_FILES['file'];

        if (!isset($file['error']) || is_array($file['error'])) {

            $validationErrors['file'] = "Invalid file parameters.";
            $data = [
                'errors' => $validationErrors,
                'old' => null,
            ];
            $this->redirect('/products/edit?id='.$id, $data);
        }

        if ($file['error'] !== UPLOAD_ERR_OK) {
            $validationErrors['file'] = "File upload error: " . $this->fileUploadErrorMessage($file['error']);
            $data = [
                'errors' => $validationErrors,
                'old' => null,
            ];
            $this->redirect('/products/edit?id='.$id, $data);
        }
        $fileName = basename($file['name']);

        $uploadDir = __DIR__ . '/../../public/uploads/products/' . $formData['store_id'] . '/';
        if (!is_dir($uploadDir)) {
            mkdir($uploadDir, 0777, true);
        }
        $fileName = $this->getUniqueFileName($uploadDir, $fileName);
        $uploadFile = $uploadDir . $fileName;
        $fileType = mime_content_type($file['tmp_name']);
        $fileSize = $file['size'];

        if (!move_uploaded_file($file['tmp_name'], $uploadFile)) {

            $validationErrors['file'] = "File upload failed.";
            $data = [
                'errors' => $validationErrors,
                'old' => null,
            ];
            $this->redirect('/products/edit?id='.$id, $data);
        }

        if (!$this->validateFile($fileType, $fileSize)) {
            unlink($uploadFile);

            $validationErrors['file'] = "File validation failed.";
            $data = [
                'errors' => $validationErrors,
                'old' => null,
            ];
            $this->redirect('/products/edit?id='.$id, $data);
        }

        $img = $fileName;
        $data['image'] = $img;

        
        if (!$this->model->updateElement($data, $id)) {
            $this->logError('Failed to udate product image');
            $this->model->rollBack();
        } else {
            $this->model->commit();
            $with = ['response' => 'success'];
            $this->redirect('/products/edit?id='.$id, $with);
            return;
        }
    } catch (Exception $e) {
        $this->model->rollBack();
        $this->logError($e->getMessage());
        return $this->redirect('/products/edit?id='.$id);
    }
    }

    public function handelNewProduct()
    {
        try {

            $this->model->beginTransaction();

            $fields = ['store_id', 'category_id', 'brand_id', 'name', 'description', 'price', 'app_percentage'];
            $formData = $this->getFormData($fields);
            $required = ['store_id', 'category_id', 'brand_id', 'name', 'price', 'app_percentage'];
            $validationErrors = $this->val->required($formData, $required);

            if (!empty($validationErrors)) {
                $data = [
                    'errors' => $validationErrors,
                    'old' => $formData,
                ];
                $this->redirect('/products/new-product', $data);
                $this->logError('Failed to validate');

                return;
            }

            $file = $_FILES['file'];

            if (!isset($file['error']) || is_array($file['error'])) {

                $validationErrors['file'] = "Invalid file parameters.";
                $this->logError('Invalid file parameters.');
                $data = [
                    'errors' => $validationErrors,
                    'old' => null,
                ];
                $this->redirect('/products/new-product', $data);

            }

            if ($file['error'] !== UPLOAD_ERR_OK) {
                $validationErrors['file'] = "File upload error: " . $this->fileUploadErrorMessage($file['error']);
                $this->logError("File upload error: " . $this->fileUploadErrorMessage($file['error']));
                $data = [
                    'errors' => $validationErrors,
                    'old' => null,
                ];
                $this->redirect('/products/new-product', $data);
            }
            $fileName = basename($file['name']);

            $uploadDir = __DIR__ . '/../../public/uploads/products/' . $formData['store_id'] . '/';
            if (!is_dir($uploadDir)) {
                mkdir($uploadDir, 0777, true);
            }
            $fileName = $this->getUniqueFileName($uploadDir, $fileName);
            $uploadFile = $uploadDir . $fileName;
            $fileType = mime_content_type($file['tmp_name']);
            $fileSize = $file['size'];

            if (!move_uploaded_file($file['tmp_name'], $uploadFile)) {

                $validationErrors['file'] = "File upload failed.";
                $this->logError('File upload failed.');

                $data = [
                    'errors' => $validationErrors,
                    'old' => null,
                ];
                $this->redirect('/products/new-product', $data);
            }

            if (!$this->validateFile($fileType, $fileSize)) {
                unlink($uploadFile);

                $validationErrors['file'] = "File validation failed.";
                $this->logError('File validation failed.');

                $data = [
                    'errors' => $validationErrors,
                    'old' => null,
                ];
                $this->redirect('/products/new-product', $data);
            }

            $img = $fileName;
            $formData['image'] = $img;

            if (!$this->model->createElement($formData)) {
                $this->logError('Failed to create store');
                $this->model->rollBack();
            } else {
                $this->model->commit();
                $with = ['response' => 'success'];
                $this->redirect('/products', $with);
                return;
            }
        } catch (Exception $e) {
            $this->model->rollBack();
            $this->logError($e->getMessage());
            return $this->redirect('/products/new-product');
        }
    }

    public function delete()
    {
        try {
            $this->model->beginTransaction();
            $id = $_GET['id'];

            if (!$this->model->deleteElement($id)) {
                $this->model->rollBack();
                return false;
            } else {
                $this->model->commit();
                $this->redirect('/products');
                return;
            }
        } catch (Exception $e) {
            $this->model->rollBack();
            $this->logError($e->getMessage());
            return $this->redirect('/error');
        }
    }
}
