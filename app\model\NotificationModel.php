<?php

class HomeModel extends BaseModel {

    public function __construct() {
        $this->table = 'notifications';
    }

    /**
     * Send notification to a single user.
     *
     * @param int $userId User ID
     * @param string $title Notification title
     * @param string $message Notification message
     * @param string $type Notification type (default: 'info')
     * @param string|null $url Optional URL
     * @param string $method Notification delivery method (default: 'database')
     * @return bool
     */
    public function sendToUser(int $userId, string $title, string $message, string $type = 'info', ?string $url = null, string $method = 'database'): bool
    {
        $notificationId = $this->createNotification($title, $message, $type, $url);
        return $this->attachUser($notificationId, $userId, $method);
    }

    /**
     * Send notification to multiple users.
     *
     * @param array $userIds List of User IDs
     * @param string $title Notification title
     * @param string $message Notification message
     * @param string $type Notification type (default: 'info')
     * @param string|null $url Optional URL
     * @param string $method Notification delivery method (default: 'database')
     * @return bool
     */
    public function sendToUsers(array $userIds, string $title, string $message, string $type = 'info', ?string $url = null, string $method = 'database'): bool
    {
        $notificationId = $this->createNotification($title, $message, $type, $url);
        foreach ($userIds as $userId) {
            $this->attachUser($notificationId, $userId, $method);
        }
        return true;
    }

    /**
     * Send notification to all users with a specific role.
     *
     * @param string $role User role
     * @param string $title Notification title
     * @param string $message Notification message
     * @param string $type Notification type (default: 'info')
     * @param string|null $url Optional URL
     * @param string $method Notification delivery method (default: 'database')
     * @return bool
     */
    public function sendToRole(string $role, string $title, string $message, string $type = 'info', ?string $url = null, string $method = 'database'): bool
    {
        $this->db->query("SELECT id FROM users WHERE role = :role");
        $this->db->bind(':role', $role);
        $users = $this->db->resultSet();

        if (empty($users)) {
            return false;
        }
        
        $userIds = array_column($users, 'id');
        return $this->sendToUsers($userIds, $title, $message, $type, $url, $method);
    }

    /**
     * Retrieve user notifications.
     *
     * @param int $userId User ID
     * @param int $limit Number of notifications to fetch (default: 10)
     * @return array
     */
    public function getUserNotifications(int $userId, int $limit = 10): array
    {
        $this->db->query(
            "SELECT n.*, nr.is_read 
             FROM notifications n 
             INNER JOIN notification_recipients nr ON n.id = nr.notification_id 
             WHERE nr.user_id = :user_id 
             ORDER BY n.created_at DESC 
             LIMIT :limit"
        );
        $this->db->bind(':user_id', $userId);
        $this->db->bind(':limit', $limit, PDO::PARAM_INT);
        
        return $this->db->resultSet();
    }

    /**
     * Get count of unread notifications for a user.
     *
     * @param int $userId User ID
     * @return int
     */
    public function getUnreadCount(int $userId): int
    {
        $this->db->query(
            "SELECT COUNT(*) AS unread 
             FROM notification_recipients 
             WHERE user_id = :user_id AND is_read = 0"
        );
        $this->db->bind(':user_id', $userId);
        
        return (int) $this->db->single()->unread;
    }

    /**
     * Mark a notification as read for a specific user.
     *
     * @param int $notificationId Notification ID
     * @param int $userId User ID
     * @return bool
     */
    public function markAsRead(int $notificationId, int $userId): bool
    {
        $this->db->query(
            "UPDATE notification_recipients 
             SET is_read = 1 
             WHERE notification_id = :notification_id AND user_id = :user_id"
        );
        $this->db->bind(':notification_id', $notificationId);
        $this->db->bind(':user_id', $userId);
        
        return $this->db->execute();
    }

    /**
     * Create a new notification.
     *
     * @param string $title Notification title
     * @param string $message Notification message
     * @param string $type Notification type
     * @param string|null $url Optional URL
     * @return int Notification ID
     */
    private function createNotification(string $title, string $message, string $type, ?string $url): int
    {
        $this->db->query(
            "INSERT INTO notifications (title, message, type, url) 
             VALUES (:title, :message, :type, :url)"
        );
        $this->db->bind(':title', $title);
        $this->db->bind(':message', $message);
        $this->db->bind(':type', $type);
        $this->db->bind(':url', $url);
        $this->db->execute();

        return (int) $this->db->lastInsertId();
    }

    /**
     * Attach a user to a notification.
     *
     * @param int $notificationId Notification ID
     * @param int $userId User ID
     * @param string $method Notification delivery method
     * @return bool
     */
    private function attachUser(int $notificationId, int $userId, string $method): bool
    {
        $this->db->query(
            "INSERT INTO notification_recipients (notification_id, user_id, delivered_via) 
             VALUES (:notification_id, :user_id, :method)"
        );
        $this->db->bind(':notification_id', $notificationId);
        $this->db->bind(':user_id', $userId);
        $this->db->bind(':method', $method);
        
        return $this->db->execute();
    }

}

?>