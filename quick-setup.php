<?php
/**
 * Wardena API - Quick Setup Script
 * This script provides a web interface for quick setup
 */

// Security check - disable in production
if (file_exists('.env') && getenv('APP_ENV') === 'production') {
    die('Setup disabled in production mode');
}

$step = $_GET['step'] ?? 1;
$message = '';
$error = '';

// Handle form submissions
if ($_POST) {
    switch ($_POST['action']) {
        case 'test_db':
            $host = $_POST['db_host'] ?? '';
            $name = $_POST['db_name'] ?? '';
            $user = $_POST['db_user'] ?? '';
            $pass = $_POST['db_pass'] ?? '';
            
            try {
                $pdo = new PDO("mysql:host={$host};dbname={$name}", $user, $pass);
                $message = "Database connection successful!";
                $step = 3;
            } catch (PDOException $e) {
                $error = "Database connection failed: " . $e->getMessage();
            }
            break;
            
        case 'create_env':
            $envContent = "# Wardena API Configuration\n";
            $envContent .= "APP_ENV=production\n";
            $envContent .= "APP_NAME=Wardena\n";
            $envContent .= "URL=" . ($_POST['app_url'] ?? '') . "\n\n";
            
            $envContent .= "# Database Configuration\n";
            $envContent .= "DB_HOST=" . ($_POST['db_host'] ?? '') . "\n";
            $envContent .= "DB_NAME=" . ($_POST['db_name'] ?? '') . "\n";
            $envContent .= "DB_USER=" . ($_POST['db_user'] ?? '') . "\n";
            $envContent .= "DB_PASS=" . ($_POST['db_pass'] ?? '') . "\n";
            $envContent .= "DB_CHARSET=utf8mb4\n\n";
            
            $envContent .= "# Security\n";
            $envContent .= "SECRET=" . bin2hex(random_bytes(32)) . "\n";
            $envContent .= "JWT_SECRET=" . bin2hex(random_bytes(32)) . "\n\n";
            
            $envContent .= "# Email Configuration\n";
            $envContent .= "MAIL_HOST=smtp.gmail.com\n";
            $envContent .= "MAIL_PORT=587\n";
            $envContent .= "MAIL_USERNAME=\n";
            $envContent .= "MAIL_PASSWORD=\n";
            $envContent .= "MAIL_ENCRYPTION=tls\n";
            $envContent .= "MAIL_FROM_ADDRESS=<EMAIL>\n";
            $envContent .= "MAIL_FROM_NAME=Wardena\n";
            
            if (file_put_contents('.env', $envContent)) {
                $message = ".env file created successfully!";
                $step = 4;
            } else {
                $error = "Failed to create .env file. Check file permissions.";
            }
            break;
            
        case 'setup_database':
            require_once 'config/config.php';
            require_once 'config/database-setup.php';
            
            try {
                $setup = new DatabaseSetup();
                ob_start();
                $setup->setup();
                $output = ob_get_clean();
                $message = "Database setup completed!<br><pre>" . htmlspecialchars($output) . "</pre>";
                $step = 5;
            } catch (Exception $e) {
                $error = "Database setup failed: " . $e->getMessage();
            }
            break;
    }
}

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Wardena API - Quick Setup</title>
    <style>
        * { box-sizing: border-box; }
        body { 
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0; padding: 20px; min-height: 100vh;
        }
        .container { 
            max-width: 600px; margin: 0 auto; 
            background: white; border-radius: 10px; 
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        .header { 
            background: #2c3e50; color: white; 
            padding: 20px; text-align: center;
        }
        .content { padding: 30px; }
        .form-group { margin-bottom: 20px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input, select, textarea { 
            width: 100%; padding: 10px; border: 1px solid #ddd; 
            border-radius: 5px; font-size: 14px;
        }
        .btn { 
            background: #3498db; color: white; padding: 12px 24px; 
            border: none; border-radius: 5px; cursor: pointer;
            font-size: 16px; width: 100%;
        }
        .btn:hover { background: #2980b9; }
        .btn-success { background: #27ae60; }
        .btn-success:hover { background: #229954; }
        .alert { 
            padding: 15px; margin-bottom: 20px; border-radius: 5px;
        }
        .alert-success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .alert-error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .steps { 
            display: flex; justify-content: space-between; 
            margin-bottom: 30px; padding: 0; list-style: none;
        }
        .steps li { 
            flex: 1; text-align: center; padding: 10px;
            background: #ecf0f1; margin: 0 2px; border-radius: 5px;
        }
        .steps li.active { background: #3498db; color: white; }
        .steps li.completed { background: #27ae60; color: white; }
        .code { 
            background: #f8f9fa; padding: 15px; border-radius: 5px;
            font-family: monospace; margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 Wardena API - Quick Setup</h1>
            <p>إعداد سريع لنظام Wardena API</p>
        </div>
        
        <div class="content">
            <ul class="steps">
                <li class="<?= $step >= 1 ? ($step > 1 ? 'completed' : 'active') : '' ?>">1. البداية</li>
                <li class="<?= $step >= 2 ? ($step > 2 ? 'completed' : 'active') : '' ?>">2. قاعدة البيانات</li>
                <li class="<?= $step >= 3 ? ($step > 3 ? 'completed' : 'active') : '' ?>">3. الإعدادات</li>
                <li class="<?= $step >= 4 ? ($step > 4 ? 'completed' : 'active') : '' ?>">4. التثبيت</li>
                <li class="<?= $step >= 5 ? 'active' : '' ?>">5. الانتهاء</li>
            </ul>
            
            <?php if ($message): ?>
                <div class="alert alert-success"><?= $message ?></div>
            <?php endif; ?>
            
            <?php if ($error): ?>
                <div class="alert alert-error"><?= $error ?></div>
            <?php endif; ?>
            
            <?php if ($step == 1): ?>
                <h2>مرحباً بك في إعداد Wardena API</h2>
                <p>هذا المعالج سيساعدك في إعداد النظام خطوة بخطوة.</p>
                
                <h3>متطلبات النظام:</h3>
                <ul>
                    <li>PHP 7.4+ ✅</li>
                    <li>MySQL 5.7+ ✅</li>
                    <li>Apache/Nginx مع mod_rewrite ✅</li>
                </ul>
                
                <a href="?step=2" class="btn">البدء في الإعداد</a>
                
            <?php elseif ($step == 2): ?>
                <h2>إعداد قاعدة البيانات</h2>
                <p>يرجى إدخال معلومات الاتصال بقاعدة البيانات:</p>
                
                <form method="POST">
                    <input type="hidden" name="action" value="test_db">
                    
                    <div class="form-group">
                        <label>خادم قاعدة البيانات:</label>
                        <input type="text" name="db_host" value="localhost" required>
                    </div>
                    
                    <div class="form-group">
                        <label>اسم قاعدة البيانات:</label>
                        <input type="text" name="db_name" value="wardena_db" required>
                    </div>
                    
                    <div class="form-group">
                        <label>اسم المستخدم:</label>
                        <input type="text" name="db_user" required>
                    </div>
                    
                    <div class="form-group">
                        <label>كلمة المرور:</label>
                        <input type="password" name="db_pass">
                    </div>
                    
                    <button type="submit" class="btn">اختبار الاتصال</button>
                </form>
                
            <?php elseif ($step == 3): ?>
                <h2>إعداد التطبيق</h2>
                <p>يرجى إدخال إعدادات التطبيق:</p>
                
                <form method="POST">
                    <input type="hidden" name="action" value="create_env">
                    <input type="hidden" name="db_host" value="<?= htmlspecialchars($_POST['db_host'] ?? '') ?>">
                    <input type="hidden" name="db_name" value="<?= htmlspecialchars($_POST['db_name'] ?? '') ?>">
                    <input type="hidden" name="db_user" value="<?= htmlspecialchars($_POST['db_user'] ?? '') ?>">
                    <input type="hidden" name="db_pass" value="<?= htmlspecialchars($_POST['db_pass'] ?? '') ?>">
                    
                    <div class="form-group">
                        <label>رابط الموقع:</label>
                        <input type="url" name="app_url" value="<?= 'http' . (isset($_SERVER['HTTPS']) ? 's' : '') . '://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']) ?>" required>
                    </div>
                    
                    <button type="submit" class="btn">إنشاء ملف الإعدادات</button>
                </form>
                
            <?php elseif ($step == 4): ?>
                <h2>تثبيت قاعدة البيانات</h2>
                <p>الآن سنقوم بإنشاء الجداول وإعداد قاعدة البيانات:</p>
                
                <form method="POST">
                    <input type="hidden" name="action" value="setup_database">
                    <button type="submit" class="btn btn-success">تثبيت قاعدة البيانات</button>
                </form>
                
            <?php elseif ($step == 5): ?>
                <h2>🎉 تم الإعداد بنجاح!</h2>
                <p>تم إعداد Wardena API بنجاح. يمكنك الآن:</p>
                
                <div class="alert alert-success">
                    <h3>معلومات تسجيل الدخول الافتراضية:</h3>
                    <p><strong>البريد الإلكتروني:</strong> <EMAIL></p>
                    <p><strong>كلمة المرور:</strong> password</p>
                    <p><strong>⚠️ يرجى تغيير كلمة المرور فوراً!</strong></p>
                </div>
                
                <h3>الخطوات التالية:</h3>
                <ol>
                    <li>احذف ملف quick-setup.php لأسباب أمنية</li>
                    <li>قم بتسجيل الدخول وتغيير كلمة مرور الأدمن</li>
                    <li>اختبر API endpoints</li>
                    <li>إعداد النسخ الاحتياطي</li>
                </ol>
                
                <div class="code">
                    <strong>اختبار API:</strong><br>
                    GET <?= BASE_URL ?? '' ?>api/health
                </div>
                
                <a href="<?= BASE_URL ?? '' ?>" class="btn btn-success">الذهاب للموقع</a>
                
            <?php endif; ?>
        </div>
    </div>
</body>
</html>
