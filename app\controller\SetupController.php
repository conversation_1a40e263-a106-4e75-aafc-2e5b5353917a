<?php

/**
 * Legacy DatabaseSetup class - DEPRECATED
 * Use config/database-setup.php instead
 */
class DatabaseSetup {

    private $dbInstance;

    public function __construct() {
        $this->dbInstance = Database::getInstance();
    }

    public function setupDatabase() {
        echo "⚠️  This setup method is deprecated.\n";
        echo "Please use the new setup script: php config/database-setup.php\n";
        echo "Or use the web interface: config/database-setup.php\n\n";

        echo "Redirecting to new setup method...\n";

        // Include the new setup script
        if (file_exists(__DIR__ . '/../../config/database-setup.php')) {
            require_once __DIR__ . '/../../config/database-setup.php';
            $newSetup = new \DatabaseSetup();
            $newSetup->setup();
        } else {
            $this->legacySetup();
        }
    }

    private function legacySetup() {
        try {
            // Create a new PDO instance without specifying a database
            $pdo = new PDO("mysql:host=" . DB_HOST, DB_USER, DB_PASS);
            $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

            // Check if the database exists
            $query = $pdo->prepare("SHOW DATABASES LIKE ?");
            $query->execute([DB_NAME]);

            if ($query->rowCount() === 0) {
                // Create the database if it doesn't exist
                $pdo->exec("CREATE DATABASE " . DB_NAME . " CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
                echo "Database " . DB_NAME . " created successfully.\n";
            } else {
                echo "Database " . DB_NAME . " already exists.\n";
            }

            // Switch to the newly created database
            $pdo->exec("USE " . DB_NAME);

            // Try to use the new SQL file first
            $sqlFile = __DIR__ . '/../../config/general.sql';
            if (file_exists($sqlFile)) {
                echo "Using new database schema from general.sql...\n";
                $this->executeSqlFile($pdo, $sqlFile);
            } else {
                echo "Falling back to legacy table creation...\n";
                $this->createTables($pdo);
            }

        } catch (PDOException $e) {
            die("Database setup failed: " . $e->getMessage());
        }
    }

    private function executeSqlFile($pdo, $sqlFile) {
        $sql = file_get_contents($sqlFile);

        // Split SQL into individual statements
        $statements = array_filter(
            array_map('trim', explode(';', $sql)),
            function($stmt) {
                return !empty($stmt) && !preg_match('/^\s*--/', $stmt);
            }
        );

        foreach ($statements as $statement) {
            if (!empty($statement)) {
                try {
                    $pdo->exec($statement);

                    // Extract table name for progress indication
                    if (preg_match('/CREATE TABLE.*?`?(\w+)`?/i', $statement, $matches)) {
                        echo "Created table: {$matches[1]}\n";
                    }
                } catch (PDOException $e) {
                    echo "Warning: " . $e->getMessage() . "\n";
                }
            }
        }
    }

    private function createTables($pdo) {
        echo "Creating basic tables (legacy mode)...\n";

        // Define the SQL for essential tables only
        $tables = [
            "users" => "
                CREATE TABLE IF NOT EXISTS users (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    username VARCHAR(100) NOT NULL UNIQUE,
                    password VARCHAR(255) NOT NULL,
                    email VARCHAR(150) UNIQUE NOT NULL,
                    phone VARCHAR(20),
                    name VARCHAR(100),
                    last_name VARCHAR(100),
                    address TEXT,
                    role ENUM('admin', 'employee', 'store_owner', 'customer', 'delivery_boy') NOT NULL DEFAULT 'customer',
                    is_active TINYINT(1) DEFAULT 1,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                )
            ",
            "stores" => "
                CREATE TABLE IF NOT EXISTS stores (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    name VARCHAR(150) NOT NULL,
                    img VARCHAR(255),
                    owner_id INT NOT NULL,
                    address TEXT,
                    is_active TINYINT(1) DEFAULT 1,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    FOREIGN KEY (owner_id) REFERENCES users(id) ON DELETE CASCADE
                )
            ",
            "product_categories" => "
                CREATE TABLE IF NOT EXISTS product_categories (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    name VARCHAR(100) NOT NULL UNIQUE,
                    img VARCHAR(255),
                    is_active TINYINT(1) DEFAULT 1,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                )
            ",
            "products" => "
                CREATE TABLE IF NOT EXISTS products (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    store_id INT NOT NULL,
                    category_id INT NOT NULL,
                    name VARCHAR(150) NOT NULL,
                    description TEXT,
                    price DECIMAL(10, 2) NOT NULL,
                    app_percentage DECIMAL(5, 2) NOT NULL DEFAULT 0.00,
                    is_active TINYINT(1) DEFAULT 1,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    FOREIGN KEY (store_id) REFERENCES stores(id) ON DELETE CASCADE,
                    FOREIGN KEY (category_id) REFERENCES product_categories(id) ON DELETE CASCADE
                )
            ",
            "orders" => "
                CREATE TABLE IF NOT EXISTS orders (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    customer_id INT NOT NULL,
                    store_id INT NOT NULL,
                    total_amount DECIMAL(10, 2) NOT NULL,
                    status ENUM('pending', 'accepted', 'in_transit', 'delivered', 'canceled') DEFAULT 'pending',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    FOREIGN KEY (customer_id) REFERENCES users(id) ON DELETE CASCADE,
                    FOREIGN KEY (store_id) REFERENCES stores(id) ON DELETE CASCADE
                )
            "
        ];

        // Execute the SQL to create each table
        foreach ($tables as $tableName => $sql) {
            try {
                $pdo->exec($sql);
                echo "Table $tableName created or already exists.\n";
            } catch (PDOException $e) {
                echo "Error creating table $tableName: " . $e->getMessage() . "\n";
            }
        }

        // Create default admin user
        $this->createDefaultAdmin($pdo);
    }

    private function createDefaultAdmin($pdo) {
        try {
            // Check if admin user exists
            $stmt = $pdo->prepare("SELECT COUNT(*) FROM users WHERE role = 'admin'");
            $stmt->execute();
            $adminCount = $stmt->fetchColumn();

            if ($adminCount == 0) {
                // Create default admin user
                $hashedPassword = password_hash('password', PASSWORD_DEFAULT);
                $stmt = $pdo->prepare("
                    INSERT INTO users (username, email, password, role, name, last_name, is_active)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                ");
                $stmt->execute([
                    'admin',
                    '<EMAIL>',
                    $hashedPassword,
                    'admin',
                    'System',
                    'Administrator',
                    1
                ]);
                echo "Default admin user created successfully.\n";
                echo "Email: <EMAIL>\n";
                echo "Password: password (Please change this immediately!)\n";
            } else {
                echo "Admin user already exists.\n";
            }
        } catch (PDOException $e) {
            echo "Error creating admin user: " . $e->getMessage() . "\n";
        }
    }
}

// Run the setup only if called directly
if (basename($_SERVER['PHP_SELF']) === 'SetupController.php' || php_sapi_name() === 'cli') {
    echo "⚠️  Using legacy setup controller.\n";
    echo "For better experience, use: php config/database-setup.php\n\n";

    $databaseSetup = new DatabaseSetup();
    $databaseSetup->setupDatabase();
}

?>
