<?php

class DatabaseSetup {

    private $dbInstance;

    public function __construct() {
        
        $this->dbInstance = Database::getInstance();
    }

    public function setupDatabase() {
        try {
            // Create a new PDO instance without specifying a database
            $pdo = new PDO("mysql:host=" . DB_HOST, DB_USER, DB_PASS);
            $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

            // Check if the database exists
            $query = $pdo->prepare("SHOW DATABASES LIKE ?");
            $query->execute([DB_NAME]);

            if ($query->rowCount() === 0) {
                // Create the database if it doesn't exist
                $pdo->exec("CREATE DATABASE " . DB_NAME);
                echo "Database " . DB_NAME . " created successfully.\n";
            } else {
                echo "Database " . DB_NAME . " already exists.\n";
            }

            // Switch to the newly created database
            $pdo->exec("USE " . DB_NAME);

            // Create the required tables
            $this->createTables($pdo);

        } catch (PDOException $e) {
            die("Database setup failed: " . $e->getMessage());
        }
    }

    private function createTables($pdo) {
        // Define the SQL for each table
        $tables = [
            "users" => "
                CREATE TABLE IF NOT EXISTS users (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    username VARCHAR(100) NOT NULL,
                    password VARCHAR(255) NOT NULL,
                    email VARCHAR(150) UNIQUE NOT NULL,
                    phone VARCHAR(20),
                    address TEXT,
                    role ENUM('admin', 'employee', 'store_owner', 'customer', 'delivery_boy') NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ",
            "stores" => "
                CREATE TABLE IF NOT EXISTS stores (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    name VARCHAR(150) NOT NULL,
                    img VARCHAR(255),
                    owner_id INT NOT NULL,
                    address TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (owner_id) REFERENCES users(id) ON DELETE CASCADE
                )
            ",
            "brands" => "
                CREATE TABLE IF NOT EXISTS brands (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    name VARCHAR(100) NOT NULL,
                    img VARCHAR(255),
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ",
            "product_categories" => "
                CREATE TABLE IF NOT EXISTS product_categories (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    name VARCHAR(100) NOT NULL,
                    img VARCHAR(255),
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ",
            "products" => "
                CREATE TABLE IF NOT EXISTS products (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    store_id INT NOT NULL,
                    category_id INT NOT NULL,
                    brand_id INT,
                    name VARCHAR(150) NOT NULL,
                    description TEXT,
                    price DECIMAL(10, 2) NOT NULL,
                    app_percentage DECIMAL(5, 2) NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (store_id) REFERENCES stores(id) ON DELETE CASCADE,
                    FOREIGN KEY (category_id) REFERENCES product_categories(id) ON DELETE CASCADE,
                    FOREIGN KEY (brand_id) REFERENCES brands(id) ON DELETE SET NULL
                )
            ",
            "orders" => "
                CREATE TABLE IF NOT EXISTS orders (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    customer_id INT NOT NULL,
                    store_id INT NOT NULL,
                    delivery_boy_id INT DEFAULT NULL,
                    total_amount DECIMAL(10, 2) NOT NULL,
                    app_earnings DECIMAL(10, 2) NOT NULL,
                    store_earnings DECIMAL(10, 2) NOT NULL,
                    delivery_fee DECIMAL(10, 2) NOT NULL,
                    status ENUM('pending', 'accepted', 'in_transit', 'delivered', 'canceled') DEFAULT 'pending',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (customer_id) REFERENCES users(id) ON DELETE CASCADE,
                    FOREIGN KEY (store_id) REFERENCES stores(id) ON DELETE CASCADE,
                    FOREIGN KEY (delivery_boy_id) REFERENCES users(id) ON DELETE SET NULL
                )
            ",
            "order_details" => "
                CREATE TABLE IF NOT EXISTS order_details (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    order_id INT NOT NULL,
                    product_id INT NOT NULL,
                    quantity INT NOT NULL,
                    price DECIMAL(10, 2) NOT NULL,
                    total_price DECIMAL(10, 2) NOT NULL,
                    FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE CASCADE,
                    FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE
                )
            ",
            "delivery_boy_payments" => "
                CREATE TABLE IF NOT EXISTS delivery_boy_payments (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    delivery_boy_id INT NOT NULL,
                    order_id INT NOT NULL,
                    earnings DECIMAL(10, 2) NOT NULL,
                    in_city BOOLEAN NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (delivery_boy_id) REFERENCES users(id) ON DELETE CASCADE,
                    FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE CASCADE
                )
            ",
            "balances" => "
                CREATE TABLE IF NOT EXISTS balances (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    user_id INT,
                    app_balance DECIMAL(10, 2) DEFAULT 0.00,
                    store_balance DECIMAL(10, 2) DEFAULT 0.00,
                    delivery_boy_balance DECIMAL(10, 2) DEFAULT 0.00,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
                )
            ",
            "admin_settings" => "
                CREATE TABLE IF NOT EXISTS admin_settings (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    in_city_rate DECIMAL(10, 2) NOT NULL,
                    out_city_rate DECIMAL(10, 2) NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            "
        ];

        // Execute the SQL to create each table
        foreach ($tables as $tableName => $sql) {
            $pdo->exec($sql);
            echo "Table $tableName created or already exists.\n";
        }
    }
}

// Run the setup
$databaseSetup = new DatabaseSetup();
$databaseSetup->setupDatabase();

?>
