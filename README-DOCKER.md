# 🐳 Wardena API - Docker Setup

## تشغيل بأمر واحد فقط! ⚡

### للويندوز:
```bash
start.bat
```

### للماك/لينكس:
```bash
chmod +x start.sh
./start.sh
```

### أو باستخدام Docker Compose مباشرة:
```bash
docker-compose up --build -d
```

## ما يحدث تلقائياً 🤖

1. **تحميل وبناء الصور** - PHP 8.0 + Apache + MySQL 8.0
2. **إعداد قاعدة البيانات** - إنشاء الجداول والبيانات الأولية
3. **تكوين الشبكة** - ربط جميع الخدمات
4. **تشغيل الخدمات** - Web server, Database, phpMyAdmin, Redis
5. **فتح المتصفح** - الوصول المباشر للتطبيق

## الخدمات المتاحة 🌐

| الخدمة | الرابط | الوصف |
|--------|--------|--------|
| **التطبيق الرئيسي** | http://localhost:8080 | Wardena API |
| **API Health** | http://localhost:8080/api/health | اختبار API |
| **phpMyAdmin** | http://localhost:8081 | إدارة قاعدة البيانات |
| **Quick Setup** | http://localhost:8080/quick-setup.php | الإعداد السريع |
| **System Test** | http://localhost:8080/test-setup.php | اختبار النظام |

## معلومات قاعدة البيانات 🗄️

```
Host: localhost:3306
Database: wardena_db
Username: wardena
Password: wardena123
Root Password: root123
```

## معلومات تسجيل الدخول 👤

```
Email: <EMAIL>
Password: password
```

## الأوامر المفيدة 🛠️

### عرض حالة الحاويات:
```bash
docker-compose ps
```

### عرض السجلات:
```bash
docker-compose logs -f
```

### إيقاف الخدمات:
```bash
docker-compose down
```

### إعادة التشغيل:
```bash
docker-compose restart
```

### الدخول لحاوية الويب:
```bash
docker exec -it wardena-web bash
```

### الدخول لحاوية قاعدة البيانات:
```bash
docker exec -it wardena-db mysql -u wardena -p
```

## استكشاف الأخطاء 🔧

### إذا فشل في التشغيل:

1. **تأكد من تثبيت Docker:**
   ```bash
   docker --version
   docker-compose --version
   ```

2. **تحقق من المنافذ:**
   ```bash
   # تأكد أن المنافذ 8080, 8081, 3306 غير مستخدمة
   netstat -an | findstr "8080"
   ```

3. **مسح الحاويات القديمة:**
   ```bash
   docker-compose down --volumes --remove-orphans
   docker system prune -f
   ```

4. **إعادة البناء:**
   ```bash
   docker-compose build --no-cache
   docker-compose up -d
   ```

### إذا كانت قاعدة البيانات لا تعمل:

```bash
# تحقق من سجلات قاعدة البيانات
docker-compose logs db

# إعادة تشغيل قاعدة البيانات فقط
docker-compose restart db
```

### إذا كان الموقع لا يفتح:

```bash
# تحقق من سجلات الويب
docker-compose logs web

# تحقق من إعدادات Apache
docker exec -it wardena-web apache2ctl configtest
```

## تخصيص الإعدادات ⚙️

### تغيير المنافذ:
عدّل في `docker-compose.yml`:
```yaml
ports:
  - "8080:80"  # غيّر 8080 للمنفذ المطلوب
```

### تغيير كلمات المرور:
عدّل في `docker-compose.yml` و `.env.docker`:
```yaml
environment:
  MYSQL_PASSWORD: your-new-password
```

### إضافة خدمات جديدة:
أضف في `docker-compose.yml`:
```yaml
services:
  your-service:
    image: your-image
    # ... configuration
```

## النسخ الاحتياطي 💾

### نسخ احتياطي لقاعدة البيانات:
```bash
docker exec wardena-db mysqldump -u wardena -pwardena123 wardena_db > backup.sql
```

### استعادة النسخة الاحتياطية:
```bash
docker exec -i wardena-db mysql -u wardena -pwardena123 wardena_db < backup.sql
```

### نسخ احتياطي للملفات:
```bash
docker cp wardena-web:/var/www/html/public/uploads ./uploads-backup
```

## الإنتاج 🚀

لاستخدام Docker في الإنتاج:

1. **غيّر كلمات المرور** في `.env.docker`
2. **فعّل HTTPS** في إعدادات Apache
3. **استخدم volumes خارجية** للبيانات المهمة
4. **إعداد مراقبة** للحاويات
5. **استخدم Docker Swarm** أو Kubernetes للتوسع

## المتطلبات 📋

- **Docker Desktop** 4.0+ 
- **Docker Compose** 2.0+
- **4GB RAM** على الأقل
- **2GB مساحة** للصور والبيانات

## الدعم الفني 📞

إذا واجهت مشاكل:

1. تحقق من سجلات Docker: `docker-compose logs`
2. راجع الوثائق: `README.md`
3. تواصل معنا: <EMAIL>

---

**🎉 استمتع بالتطوير مع Docker!**
