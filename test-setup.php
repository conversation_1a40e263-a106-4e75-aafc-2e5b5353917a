<?php
/**
 * Wardena API - System Test Script
 * This script tests the system configuration and database connectivity
 */

echo "🧪 Wardena API System Test\n";
echo "==========================\n\n";

// Test 1: PHP Version
echo "1. Testing PHP Version...\n";
$phpVersion = phpversion();
echo "   Current PHP Version: {$phpVersion}\n";
if (version_compare($phpVersion, '7.4.0', '>=')) {
    echo "   ✅ PHP Version OK\n";
} else {
    echo "   ❌ PHP Version too old. Minimum required: 7.4.0\n";
}
echo "\n";

// Test 2: Required PHP Extensions
echo "2. Testing PHP Extensions...\n";
$requiredExtensions = ['pdo', 'pdo_mysql', 'json', 'openssl', 'curl', 'fileinfo', 'mbstring'];
$missingExtensions = [];

foreach ($requiredExtensions as $ext) {
    if (extension_loaded($ext)) {
        echo "   ✅ {$ext}\n";
    } else {
        echo "   ❌ {$ext} - MISSING\n";
        $missingExtensions[] = $ext;
    }
}

if (empty($missingExtensions)) {
    echo "   ✅ All required extensions are loaded\n";
} else {
    echo "   ❌ Missing extensions: " . implode(', ', $missingExtensions) . "\n";
}
echo "\n";

// Test 3: File Permissions
echo "3. Testing File Permissions...\n";
$directories = [
    'public/uploads/' => 'Uploads directory',
    'storage/' => 'Storage directory',
    'config/' => 'Config directory'
];

foreach ($directories as $dir => $description) {
    if (is_dir($dir)) {
        if (is_writable($dir)) {
            echo "   ✅ {$description} - Writable\n";
        } else {
            echo "   ⚠️  {$description} - Not writable\n";
        }
    } else {
        echo "   ❌ {$description} - Directory not found\n";
    }
}
echo "\n";

// Test 4: Configuration Files
echo "4. Testing Configuration Files...\n";
$configFiles = [
    '.env' => 'Environment configuration',
    'config/config.php' => 'Main configuration',
    'config/general.sql' => 'Database schema'
];

foreach ($configFiles as $file => $description) {
    if (file_exists($file)) {
        echo "   ✅ {$description} - Found\n";
    } else {
        echo "   ❌ {$description} - Missing\n";
    }
}
echo "\n";

// Test 5: Environment Variables
echo "5. Testing Environment Variables...\n";
if (file_exists('.env')) {
    require_once 'config/config.php';
    
    $envVars = [
        'DB_HOST' => DB_HOST,
        'DB_NAME' => DB_NAME,
        'DB_USER' => DB_USER,
        'DB_PASS' => DB_PASS,
        'BASE_URL' => BASE_URL,
        'SECRET_KEY' => SECRET_KEY
    ];
    
    foreach ($envVars as $var => $value) {
        if (!empty($value)) {
            echo "   ✅ {$var} - Set\n";
        } else {
            echo "   ❌ {$var} - Not set or empty\n";
        }
    }
} else {
    echo "   ❌ .env file not found\n";
}
echo "\n";

// Test 6: Database Connection
echo "6. Testing Database Connection...\n";
if (defined('DB_HOST') && defined('DB_NAME') && defined('DB_USER') && defined('DB_PASS')) {
    try {
        $pdo = new PDO(
            "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4",
            DB_USER,
            DB_PASS,
            [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
            ]
        );
        echo "   ✅ Database connection successful\n";
        
        // Test database tables
        $stmt = $pdo->query("SHOW TABLES");
        $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
        echo "   📊 Found " . count($tables) . " tables\n";
        
        // Check for key tables
        $keyTables = ['users', 'stores', 'products', 'orders'];
        $missingTables = [];
        
        foreach ($keyTables as $table) {
            if (in_array($table, $tables)) {
                echo "   ✅ Table '{$table}' exists\n";
            } else {
                echo "   ❌ Table '{$table}' missing\n";
                $missingTables[] = $table;
            }
        }
        
        if (empty($missingTables)) {
            echo "   ✅ All key tables exist\n";
        } else {
            echo "   ⚠️  Missing tables: " . implode(', ', $missingTables) . "\n";
            echo "   💡 Run: php config/database-setup.php\n";
        }
        
    } catch (PDOException $e) {
        echo "   ❌ Database connection failed: " . $e->getMessage() . "\n";
    }
} else {
    echo "   ❌ Database configuration not found\n";
}
echo "\n";

// Test 7: Web Server Configuration
echo "7. Testing Web Server Configuration...\n";
if (isset($_SERVER['SERVER_SOFTWARE'])) {
    echo "   Server: " . $_SERVER['SERVER_SOFTWARE'] . "\n";
} else {
    echo "   Running from command line\n";
}

if (file_exists('.htaccess')) {
    echo "   ✅ Main .htaccess file exists\n";
} else {
    echo "   ❌ Main .htaccess file missing\n";
}

if (file_exists('public/.htaccess')) {
    echo "   ✅ Public .htaccess file exists\n";
} else {
    echo "   ❌ Public .htaccess file missing\n";
}
echo "\n";

// Test 8: API Endpoint Test (if running via web server)
echo "8. Testing API Endpoints...\n";
if (isset($_SERVER['HTTP_HOST'])) {
    $baseUrl = (isset($_SERVER['HTTPS']) ? 'https' : 'http') . '://' . $_SERVER['HTTP_HOST'];
    $testUrl = $baseUrl . '/api/health';
    
    echo "   Testing: {$testUrl}\n";
    
    $context = stream_context_create([
        'http' => [
            'timeout' => 5,
            'ignore_errors' => true
        ]
    ]);
    
    $response = @file_get_contents($testUrl, false, $context);
    
    if ($response !== false) {
        echo "   ✅ API endpoint accessible\n";
        echo "   Response: " . substr($response, 0, 100) . "\n";
    } else {
        echo "   ⚠️  API endpoint not accessible (this is normal if running from CLI)\n";
    }
} else {
    echo "   ⚠️  Running from command line - cannot test web endpoints\n";
}
echo "\n";

// Summary
echo "🏁 Test Summary\n";
echo "===============\n";

$allGood = true;

// Check critical issues
if (version_compare($phpVersion, '7.4.0', '<')) {
    echo "❌ CRITICAL: PHP version too old\n";
    $allGood = false;
}

if (!empty($missingExtensions)) {
    echo "❌ CRITICAL: Missing PHP extensions\n";
    $allGood = false;
}

if (!file_exists('.env')) {
    echo "❌ CRITICAL: .env file missing\n";
    $allGood = false;
}

if (!defined('DB_HOST') || empty(DB_HOST)) {
    echo "❌ CRITICAL: Database configuration missing\n";
    $allGood = false;
}

if ($allGood) {
    echo "✅ System appears to be configured correctly!\n";
    echo "💡 Next steps:\n";
    echo "   1. Run: php config/database-setup.php (if tables are missing)\n";
    echo "   2. Test the API endpoints via web browser\n";
    echo "   3. Check the application logs for any issues\n";
} else {
    echo "❌ System has configuration issues that need to be resolved\n";
    echo "💡 Please fix the critical issues above before proceeding\n";
}

echo "\n🔗 For detailed setup instructions, see:\n";
echo "   - INSTALLATION.md\n";
echo "   - config/hosting-setup.md\n";
echo "\n";
?>
