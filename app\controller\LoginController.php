<?php

class LoginController extends BaseController
{

    private $loginModel;

    public function __construct($request, $response)
    {
        parent::__construct($request, $response);
        $this->loginModel = new LoginModel();
    }

    public function index()
    {

        if ($this->isLoggedIn()) {
            $this->redirect('/home');
        } elseif (isset($_COOKIE['remember_me_wardena'])) {
            $cookieData = json_decode($_COOKIE['remember_me_wardena'], true);
            $userId = $cookieData['user_id'];
            $token = $cookieData['token'];

            $hashedTokenFromDB = $this->loginModel->getRememberToken($userId);

            if (hash('sha256', $token) === $hashedTokenFromDB) {
                $user = $this->loginModel->getUserById($userId);
                $this->setSession('user_id', $user['id']);
                $this->setSession('user_role', $user['role']);
                $this->setSession('user_name', $user['last_name']);

                $this->loginModel->deleteRememberToken($userId);

                $newToken = bin2hex(random_bytes(16));
                $hashedToken = hash('sha256', $newToken);
                $expiresAt = date("Y-m-d H:i:s", time() + (30 * 24 * 60 * 60));

                $this->loginModel->storeRememberToken($userId, $hashedToken, $expiresAt);

                setcookie(
                    'remember_me_wardena',
                    json_encode(['user_id' => $userId, 'token' => $newToken]),
                    time() + (30 * 24 * 60 * 60),
                    "/",
                    "",
                    false,
                    true
                );

                $this->redirect('/home');
            }
        } elseif ($this->isPost()) {
            $this->handleLogin();
        } else {
            $data = [
                'title' => 'Login | wardena - Dashboard',
            ];
            $this->render('../app/view/login.php', $data);
        }
    }

    public function api()
    {

        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            http_response_code(405);
            echo json_encode(['error' => 'Method not allowed']);
            return;
        }

        $input = json_decode(file_get_contents('php://input'), true);

        $username = $input['email'] ?? null;
        $password = $input['password'] ?? null;

        if (empty($username) || empty($password)) {
            http_response_code(400);
            echo json_encode(['error' => 'Username and password are required']);
            return;
        }

        $formData = ['email' => $username, 'password' => $password];

        $validationErrors = $this->loginModel->validateUser($formData);

        if ($validationErrors['success'] == false) {
            http_response_code(401);
            echo json_encode(['error' => 'Invalid username or password']);
            return;
        } elseif ($validationErrors['success'] == true) {
            $user = $this->loginModel->getUserByUsername($formData['email']);

            $jwt = $this->generateJwt($user['id'], $user['role']);

             echo json_encode([
                'message' => 'Login successful',
                'token' => $jwt
            ]);

        } else {
            $this->setAlert('There was a problem during login. Please try again!', 'error');
        }
    }

    private function handleLogin()
    {
        $fields = ['email', 'password', 'remember_me'];
        $formData = $this->getFormData($fields);
        $validationErrors = $this->loginModel->validateUser($formData);

        if ($validationErrors['success'] == false) {
            $data = [
                'title' => 'Login | wardena - PearlFibers',
                'errors' => $validationErrors['errors'],
                'old' => $formData,
            ];
            $this->render('../app/view/login.php', $data);
            return;
        } elseif ($validationErrors['success'] == true) {
            $user = $this->loginModel->getUserByUsername($formData['email']);
            $this->setSession('user_id', $user['id']);
            $this->setSession('role', $user['role']);
            $this->setSession('user_name', $user['last_name']);

            if (isset($formData['remember_me']) && $formData['remember_me'] == true) {
                $token = bin2hex(random_bytes(16));
                $hashedToken = hash('sha256', $token);
                $expiresAt = date("Y-m-d H:i:s", time() + (30 * 24 * 60 * 60));

                $this->loginModel->storeRememberToken($user['id'], $hashedToken, $expiresAt);
                setcookie('remember_me_wardena', json_encode(['user_id' => $user['id'], 'token' => $token]), time() + (30 * 24 * 60 * 60), "/", "", false, true);
            }

            $this->redirect('/home');
        } else {
            $this->setAlert('There was a problem during login. Please try again!', 'error');
        }
        
    }

    public function logout()
    {
        $userId = $this->getSessionData('user_id') ?? null;
        if (isset($_COOKIE['remember_me_wardena'])) {
            $this->loginModel->deleteRememberToken($userId);
            setcookie('remember_me_wardena', '', time() - 3600, '/');
        }
        $_SESSION = array();
        if (ini_get("session.use_cookies")) {
            $params = session_get_cookie_params();
            setcookie(
                session_name(),
                '',
                time() - 42000,
                $params["path"],
                $params["domain"],
                $params["secure"],
                $params["httponly"]
            );
        }
        session_destroy();
        $this->redirect('/home');
    }
}
