<?php

class ChatController extends BaseController
{
    private $model;
    private $users;
    private $statusFile;


    public function __construct($request, $response)
    {
        parent::__construct($request, $response);
        $this->model = new DashboardModel();
        $this->users = new CrudModel('users');
        $this->statusFile = __DIR__ . '/../../storage/online_status.json';
    }

    public function index()
    {
        $userId = $this->getSessionData('user_id');
        $user = $this->model->getUserData($userId);

        $data = [
            'title' => 'Home',
            'style' => null,
            'user' => $user,
        ];

        $this->render('../app/view/admin/chat.php', $data);
    }

    public function updateStatus()
    {
        header("Content-Type: application/json");
        $input = json_decode(file_get_contents("php://input"), true);

        if (!isset($input['sessionId'])) {
            http_response_code(400);
            echo json_encode(["error" => "Session ID is required"]);
            return;
        }

        $sessionId = htmlspecialchars($input['sessionId'], ENT_QUOTES, 'UTF-8');

        $statusData = file_exists($this->statusFile) ? json_decode(file_get_contents($this->statusFile), true) : [];

        $statusData[$sessionId] = [
            "sessionId" => $sessionId,
            "lastActive" => time()
        ];

        file_put_contents($this->statusFile, json_encode($statusData, JSON_PRETTY_PRINT));
        echo json_encode(["message" => "Status updated"]);
    }

    public function getOnlineUsers()
    {
        header("Content-Type: application/json");

        $statusData = file_exists($this->statusFile) ? json_decode(file_get_contents($this->statusFile), true) : [];

        $onlineUsers = [];
        $currentTime = time();
        $threshold = 120;

        foreach ($statusData as $session) {
            if (($currentTime - $session['lastActive']) <= $threshold) {
                $onlineUsers[] = $session;
            }
        }

        echo json_encode(["onlineUsers" => $onlineUsers]);
    }

    public function submitChat() {
        $this->handleChatMessage($_POST, $_FILES, false);
    }
    

    public function submitReplay()
    {
        $this->handleChatMessage($_POST, $_FILES, true);
    }

    private function handleChatMessage($postData, $files, $isReplay = false)
    {
        $uploadDir = __DIR__ . '/../../public/uploads/chat/';
        $dbFile = __DIR__ . '/../../storage/chat.json';
    
        try {
            // Validate required fields
            if (!isset($postData['sessionId'], $postData['sender'], $postData['type'])) {
                throw new Exception("Missing required fields", 400);
            }
    
            // Sanitize input
            $sessionId = htmlspecialchars($postData['sessionId'], ENT_QUOTES, 'UTF-8');
            $sender = htmlspecialchars($postData['sender'], ENT_QUOTES, 'UTF-8');
            $type = htmlspecialchars($postData['type'], ENT_QUOTES, 'UTF-8');
            $content = isset($postData['content']) ? htmlspecialchars($postData['content'], ENT_QUOTES, 'UTF-8') : null;
    
            // Handle file uploads
            if (isset($files['file'])) {
                if (!is_dir($uploadDir) && !mkdir($uploadDir, 0755, true) && !is_dir($uploadDir)) {
                    throw new Exception("Failed to create upload directory", 500);
                }
    
                $file = $files['file'];
                $extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
                $allowedExtensions = ['jpg', 'jpeg', 'png', 'webm'];
    
                if (!in_array($extension, $allowedExtensions) || $file['size'] > (5 * 1024 * 1024)) {
                    throw new Exception("Invalid file type or size", 400);
                }
    
                $uniqueName = uniqid() . '.' . $extension;
                $filePath = $uploadDir . basename($uniqueName);
    
                if (!move_uploaded_file($file['tmp_name'], $filePath)) {
                    throw new Exception("Failed to save file", 500);
                }
    
                $content = basename($uniqueName);
            }
    
            // Prepare the message entry
            $messageEntry = [
                "messageId" => uniqid("msg-"),
                "sender" => $sender,
                "timestamp" => date("c"),
                "type" => $type,
                "content" => $content
            ];
    
            // Initialize chat database
            if (!file_exists($dbFile)) {
                $chatDatabase = ["chatSessions" => []];
            } else {
                // Read and decode existing chat data
                $chatData = file_get_contents($dbFile);
                if ($chatData === false) {
                    throw new Exception("Failed to read database file", 500);
                }
    
                $chatDatabase = json_decode($chatData, true);
                if (json_last_error() !== JSON_ERROR_NONE) {
                    throw new Exception("Corrupt database file: " . json_last_error_msg(), 500);
                }
            }
    
            // Locate existing session
            $sessionKey = array_search($sessionId, array_column($chatDatabase['chatSessions'], 'sessionId'), true);
    
            if ($sessionKey === false) {
                if (!$isReplay) {
                    $chatDatabase['chatSessions'][] = [
                        "sessionId" => $sessionId,
                        "userName" => $sender,
                        "startTime" => date("c"),
                        "unreadCount" => 1,
                        "messages" => [$messageEntry]
                    ];
                } else {
                    throw new Exception("Session not found", 400);
                }
            } else {
                $chatDatabase['chatSessions'][$sessionKey]['unreadCount'] += 1;
                $chatDatabase['chatSessions'][$sessionKey]['messages'][] = $messageEntry;
            }
    
            // Save the updated database using file lock
            $fp = fopen($dbFile, "c+");
            if (!$fp) {
                throw new Exception("Failed to open database file", 500);
            }
    
            if (flock($fp, LOCK_EX)) {
                ftruncate($fp, 0); // Clear file before writing
                fwrite($fp, json_encode($chatDatabase, JSON_PRETTY_PRINT));
                fflush($fp);
                flock($fp, LOCK_UN);
            } else {
                throw new Exception("Failed to lock database file", 500);
            }
            fclose($fp);
    
            echo json_encode(["message" => "Message saved", "url" => $messageEntry["content"]]);
        } catch (Exception $e) {
            http_response_code($e->getCode() ?: 513);
            error_log("Chat Error: " . $e->getMessage());
            echo json_encode(["error" => $e->getMessage()]);
        }
    }
    
    public function chatData()
    {
        header("Content-Type: application/json");
        header("Access-Control-Allow-Origin: *");

        $chatDatabaseFile =  __DIR__ . '/../../storage/chat.json';

        if (file_exists($chatDatabaseFile)) {
            $chatData = json_decode(file_get_contents($chatDatabaseFile), true);

            if (!isset($chatData['chatSessions'])) {
                $chatData['chatSessions'] = [];
            }

            $lastUpdated = isset($_GET['lastUpdated']) ? $_GET['lastUpdated'] : null;
            $newData = array_map(function ($session) use ($lastUpdated) {
                if (!isset($session['messages'])) {
                    $session['messages'] = [];
                }
                $session['messages'] = array_filter($session['messages'], function ($message) use ($lastUpdated) {
                    return !$lastUpdated || strtotime($message['timestamp']) > strtotime($lastUpdated);
                });
                return $session;
            }, $chatData['chatSessions']);

            echo json_encode([
                "chatSessions" => $newData,
                "lastUpdated" => date("c")
            ]);
        } else {
            echo json_encode(["chatSessions" => [], "lastUpdated" => date("c")]);
        }
    }
}
