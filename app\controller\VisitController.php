<?php

class VisitController extends BaseController {

    private $model;
    private $dashboard;

    public function __construct()
    {
        $this->model = new CrudModel('visits');
        $this->dashboard = new DashboardModel();

        
    }
    public function trackVisit($pageVisited) {

        if ($pageVisited !== '/' && strpos($pageVisited, '/home') !== 0) {
            return; 
        }

        $ipAddress = $_SERVER['REMOTE_ADDR'];
        $userAgent = $_SERVER['HTTP_USER_AGENT'];
        $visitTime = date('Y-m-d H:i:s');
        $referrer = isset($_SERVER['HTTP_REFERER']) ? $_SERVER['HTTP_REFERER'] : null;
        
        $maxReferrerLength = 255;
        $maxPageVisitedLength = 255;
        
        $referrer = $referrer ? substr($referrer, 0, $maxReferrerLength) : null;
        $pageVisited = isset($pageVisited) ? substr($pageVisited, 0, $maxPageVisitedLength) : null;
        
        try {
            $this->model->beginTransaction();
        
            $data = [
                'ip_address' => $ipAddress,
                'user_agent' => $userAgent,
                'visit_time' => $visitTime,
                'page_visited' => $pageVisited,
                'referrer' => $referrer,
            ];
        
            if (!$this->model->createElement($data)) {
                $this->logError('Failed to add Visit');
                $this->model->rollBack();
                return;
            }
        
            $this->model->commit();
        } catch (Exception $e) {
            $this->model->rollBack();
            $this->logError($e->getMessage());
            return $this->redirect('/error');
        }

    }

    public function getVisitStats() {

        $data = $this->dashboard->getVisitsByDay();

        header('Content-Type: application/json');
        echo json_encode($data);
        exit;

    }


    public function generateFakeData() {
        $days = 40;
        $maxVisits = 12;

        try {
            $this->model->beginTransaction();

            for ($i = 0; $i < $days; $i++) {

                $date = date('Y-m-d', strtotime("-$i days"));


                if (rand(0, 3) === 0) {
                    $visitsToday = 0;
                } else {
                    $visitsToday = rand(1, $maxVisits); 
                }

                if ($visitsToday > 0 && $visitsToday < 10 && rand(0, 1) === 1) {
                    $visitsToday = rand(5, $maxVisits); 
                }

                for ($j = 0; $j < $visitsToday; $j++) {
                    $data = [
                        'ip_address' => '192.168.' . rand(0, 255) . '.' . rand(0, 255), 
                        'user_agent' => 'FakeUserAgent/1.0',
                        'visit_time' => $date . ' ' . rand(0, 23) . ':' . str_pad(rand(0, 59), 2, '0', STR_PAD_LEFT) . ':00', 
                        'page_visited' => '/fake-page', 
                        'referrer' => null, 
                    ];

                    if (!$this->model->createElement($data)) {
                        $this->logError('Failed to insert fake visit');
                        $this->model->rollBack();
                        return;
                    }
                }

                echo "Inserted $visitsToday visits for $date\n";
            }

            $this->model->commit();
            echo "Fake data generation complete!";
        } catch (Exception $e) {
            $this->model->rollBack();
            $this->logError($e->getMessage());
            echo "An error occurred while generating fake data: " . $e->getMessage();
        }
    }

}