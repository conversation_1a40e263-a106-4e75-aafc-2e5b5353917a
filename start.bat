@echo off
REM ===================================
REM Wardena API - Docker Startup Script (Windows)
REM ===================================

echo 🚀 Starting Wardena API with Docker...
echo ======================================

REM التحقق من وجود Docker
docker --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Docker is not installed. Please install Docker Desktop first.
    echo    Download from: https://www.docker.com/products/docker-desktop
    pause
    exit /b 1
)

REM التحقق من وجود Docker Compose
docker-compose --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Docker Compose is not installed. Please install Docker Compose first.
    pause
    exit /b 1
)

REM إنشاء المجلدات المطلوبة
echo 📁 Creating required directories...
if not exist "storage\cache" mkdir storage\cache
if not exist "storage\logs" mkdir storage\logs
if not exist "public\uploads" mkdir public\uploads
if not exist "docker\apache" mkdir docker\apache
if not exist "docker\php" mkdir docker\php
if not exist "docker\mysql" mkdir docker\mysql

REM نسخ ملف البيئة للـ Docker
echo ⚙️ Setting up environment...
if not exist ".env" (
    copy ".env.docker" ".env"
    echo ✅ Created .env file from .env.docker
) else (
    echo ℹ️ .env file already exists
)

REM إيقاف الحاويات السابقة (إن وجدت)
echo 🛑 Stopping existing containers...
docker-compose down --remove-orphans

REM بناء وتشغيل الحاويات
echo 🔨 Building and starting containers...
docker-compose up --build -d

REM انتظار تشغيل قاعدة البيانات
echo ⏳ Waiting for database to be ready...
timeout /t 30 /nobreak >nul

REM التحقق من حالة الحاويات
echo 📊 Checking container status...
docker-compose ps

REM عرض معلومات الوصول
echo.
echo 🎉 Wardena API is now running!
echo ================================
echo 🌐 Main Application: http://localhost:8080
echo 🔧 API Health Check: http://localhost:8080/api/health
echo 🗄️ phpMyAdmin: http://localhost:8081
echo 📊 Quick Setup: http://localhost:8080/quick-setup.php
echo 🧪 System Test: http://localhost:8080/test-setup.php
echo.
echo 📋 Database Information:
echo    Host: localhost:3306
echo    Database: wardena_db
echo    Username: wardena
echo    Password: wardena123
echo.
echo 👤 Default Admin Login:
echo    Email: <EMAIL>
echo    Password: password
echo.
echo 🛑 To stop: docker-compose down
echo 📝 To view logs: docker-compose logs -f
echo 🔄 To restart: start.bat

REM فتح المتصفح تلقائياً
echo 🌐 Opening browser...
start http://localhost:8080

echo.
echo ✅ Setup completed successfully!
echo Press any key to continue...
pause >nul
