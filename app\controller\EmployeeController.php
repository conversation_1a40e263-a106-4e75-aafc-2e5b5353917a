<?php

class EmployeeController extends BaseController
{

    private $model;
    private $employee;
    private $users;
    private $auth;
    private $products;
    private $orders;
    private $service;
    private $productService;
    private $categories;

    private $brands;
    private $stores;
    private $valProduct;

    private $orderService;

    public function __construct($request, $response)
    {
        parent::__construct($request, $response);
        $this->model = new CrudModel('users');
        $this->employee = new CrudModel('stores');
        $this->products = new CrudModel('products');
        $this->users = new CrudModel('users');
        $this->orders = new CrudModel('orders');
        $this->auth = new AuthService();
        $this->service = new StoreService();
        $this->productService = new ProductService();
        $this->categories = new CrudModel('product_categories');

        $this->brands = new CrudModel('brands');
        $this->stores = new CrudModel('stores');
        $this->valProduct = new ValidateModel('products');

        $this->orderService = new OrderService();

    }

    public function index()
    {
        if ($this->auth->employee() == true) {
            $userId = $this->getSessionData('user_id');
            $user = $this->users->getElement($userId);
            $store = $this->employee->getElementsWhere('owner_id', $userId);
            $productsCount = $this->products->countAll();
            $ordersCount = $this->orders->countAll();

            $count = [
                'products' => $productsCount,
                'orders' => $ordersCount,
            ];
            $data = [
                'title' => 'Store Management Dashboard',
                'style' => null,
                'user' => $user,
                'store' => $store,
                'count' => $count
            ];

            $this->render('../app/view/employee/dashboard.php', $data);
        } else {
            $this->redirect('/login');
        }
    }

}
