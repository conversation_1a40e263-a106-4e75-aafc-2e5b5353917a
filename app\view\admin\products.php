<!--start content-->
<main class="page-content">
    <!--breadcrumb-->
    <div class="page-breadcrumb d-none d-sm-flex align-items-center mb-3">
        <div class="breadcrumb-title pe-3">لوحة التحكم</div>
        <div class="ps-3">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb mb-0 p-0">
                    <li class="breadcrumb-item"><a href="javascript:;"><i class="bx bx-home-alt"></i></a>
                    </li>
                    <li class="breadcrumb-item active" aria-current="page">المنتجات</li>
                </ol>
            </nav>
        </div>
    </div>
    <!--end breadcrumb-->

    <div class="card">
        <div class="card-body">
            <div class="row align-items-center">
                <div class="col-lg-3 col-xl-2">
                    <a href="/products/new-product" class="btn btn-primary mb-3 mb-lg-0"><i class="bi bi-plus-square-fill"></i> اضافة منتج</a>
                </div>
            </div>
        </div>
    </div>

    <div class="card">
        <div class="card-header py-3">
            <div class="row g-3 align-items-center">
                <div class="col-lg-3 col-md-6 me-auto">
                    <div class="ms-auto position-relative">
                        <div class="position-absolute top-50 translate-middle-y search-icon px-3"><i class="bi bi-search"></i>
                        </div>
                        <input class="form-control ps-5" type="text" placeholder="search produts">
                    </div>
                </div>
                <div class="col-lg-2 col-6 col-md-3">
                    <form method="GET" action="/products">
                        <select class="form-select" name="category_id" onchange="this.form.submit()">
                            <option value="">كل الفئات</option>
                            <?php
                            foreach ($categories as $category) {
                                $selected = isset($_GET['category_id']) && $_GET['category_id'] == $category['id'] ? 'selected' : '';
                                echo '<option value="' . $category['id'] . '" ' . $selected . '>' . $category['prefix'] . '</option>';
                            }
                            ?>
                        </select>
                    </form>
                </div>
            </div>
        </div>
        <div class="card-body">
            <div class="product-grid">
                <div class="row row-cols-1 row-cols-lg-4 row-cols-xl-4 row-cols-xxl-5 g-3">
                    <?php
                    foreach ($result as $product) {
                    ?>
                        <div class="col">
                            <div class="card border shadow-none mb-0">
                                <div class="card-body text-center">
                                    <div class="product-image-wrapper mb-3">
                                        <img src="/uploads/products/<?php echo $product['store_id'];?>/<?php echo $product['image'];?>" class="img-fluid mb-3 product-image" alt="" />
                                    </div>
                                    <h6 class="product-title"><?php echo $product['name'];?></h6>
                                    <p class="product-price fs-5 mb-1"><span><?php echo $product['price'];?></span> د.ع</p>
                                    <small><?php echo $product['store'];?></small>
                                    <div class="actions d-flex align-items-center justify-content-center gap-2 mt-3">
                                        <a href="/products/edit?id=<?php echo $product['id'];?>" class="btn btn-sm btn-outline-primary"><i class="bi bi-pencil-fill"></i>
                                            تعديل</a>
                                        <a href="/products/delete?id=<?php echo $product['id'];?>" class="btn btn-sm btn-outline-danger"><i class="bi bi-trash-fill"></i> حذف</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php
                    }
                    ?>
                </div><!--end row-->
            </div>
            
            <nav class="float-end mt-4" aria-label="Page navigation">
                <ul class="pagination">
                    <!-- Previous Page Link -->
                    <li class="page-item <?= $currentPage <= 1 ? 'disabled' : '' ?>">
                        <a class="page-link" href="?category_id=<?= $_GET['category_id'] ?? '' ?>&page=<?= $currentPage - 1 ?>">السابق</a>
                    </li>
                    
                    <!-- Page Number Links -->
                    <?php for ($i = 1; $i <= $totalPages; $i++) { ?>
                        <li class="page-item <?= $i == $currentPage ? 'active' : '' ?>">
                            <a class="page-link" href="?category_id=<?= $_GET['category_id'] ?? '' ?>&page=<?= $i ?>"><?= $i ?></a>
                        </li>
                    <?php } ?>
                    
                    <!-- Next Page Link -->
                    <li class="page-item <?= $currentPage >= $totalPages ? 'disabled' : '' ?>">
                        <a class="page-link" href="?category_id=<?= $_GET['category_id'] ?? '' ?>&page=<?= $currentPage + 1 ?>">التالي</a>
                    </li>
                </ul>
            </nav>
        </div>
    </div>

</main>
<!--end page main-->