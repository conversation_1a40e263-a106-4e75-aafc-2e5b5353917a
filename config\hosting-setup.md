# دليل إعداد Wardena API على الاستضافة الخارجية

## متطلبات الاستضافة

### الحد الأدنى من المتطلبات:
- **PHP**: 7.4 أو أحدث (يُفضل PHP 8.0+)
- **MySQL**: 5.7 أو أحدث (يُفضل MySQL 8.0+)
- **Apache/Nginx**: مع دعم mod_rewrite
- **مساحة التخزين**: 500 ميجابايت على الأقل
- **الذاكرة**: 128 ميجابايت على الأقل

### الإضافات المطلوبة لـ PHP:
```
- PDO
- PDO_MySQL
- JSON
- OpenSSL
- cURL
- GD أو ImageMagick
- Fileinfo
- Mbstring
```

## خطوات الإعداد

### 1. رفع الملفات
1. ارفع جميع ملفات المشروع إلى مجلد الجذر للموقع
2. تأكد من أن مجلد `public` هو المجلد الرئيسي للموقع
3. إذا كان موقعك في مجلد فرعي، قم بتعديل مسارات الملفات

### 2. إعداد قاعدة البيانات
1. أنشئ قاعدة بيانات جديدة من لوحة التحكم (cPanel)
2. أنشئ مستخدم قاعدة بيانات وأعطه جميع الصلاحيات
3. سجل معلومات الاتصال (اسم الخادم، اسم قاعدة البيانات، اسم المستخدم، كلمة المرور)

### 3. تحديث ملف .env
```env
# معلومات قاعدة البيانات
DB_HOST=your-database-host
DB_NAME=your_database_name
DB_USER=your_database_user
DB_PASS=your_secure_password
DB_PORT=3306

# رابط الموقع
URL=https://yourdomain.com/
API_URL=https://yourdomain.com/api/

# البيئة
APP_ENV=production
```

### 4. تشغيل إعداد قاعدة البيانات
قم بزيارة: `https://yourdomain.com/setup`

أو قم بتشغيل ملف SQL مباشرة:
```bash
mysql -u username -p database_name < config/general.sql
```

### 5. إعداد الصلاحيات
```bash
chmod 755 public/
chmod 755 public/uploads/
chmod 755 storage/
chmod 644 .env
chmod 644 config/
```

## إعدادات Apache (.htaccess)

### في المجلد الرئيسي:
```apache
RewriteEngine On
RewriteCond %{REQUEST_URI} !^/public/
RewriteRule ^(.*)$ /public/$1 [L]
```

### في مجلد public:
```apache
RewriteEngine On
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^(.*)$ index.php [QSA,L]

# Security Headers
Header always set X-Content-Type-Options nosniff
Header always set X-Frame-Options DENY
Header always set X-XSS-Protection "1; mode=block"

# File Upload Limits
php_value upload_max_filesize 10M
php_value post_max_size 10M
php_value max_execution_time 300
php_value memory_limit 256M
```

## إعدادات Nginx

```nginx
server {
    listen 80;
    server_name yourdomain.com;
    root /path/to/your/project/public;
    index index.php;

    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php8.0-fpm.sock;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
        include fastcgi_params;
    }

    location ~ /\.ht {
        deny all;
    }
}
```

## الأمان والحماية

### 1. حماية الملفات الحساسة
```apache
# في .htaccess الرئيسي
<Files ".env">
    Order allow,deny
    Deny from all
</Files>

<Files "*.log">
    Order allow,deny
    Deny from all
</Files>
```

### 2. تحديث كلمات المرور
- غيّر كلمة مرور قاعدة البيانات
- غيّر SECRET في ملف .env
- غيّر JWT_SECRET في ملف .env

### 3. تفعيل HTTPS
- احصل على شهادة SSL
- أعد توجيه HTTP إلى HTTPS
- حدث URL في ملف .env

## استكشاف الأخطاء

### مشاكل شائعة:

1. **خطأ 500 Internal Server Error**
   - تحقق من ملف error.log
   - تأكد من صلاحيات الملفات
   - تحقق من إعدادات PHP

2. **خطأ في الاتصال بقاعدة البيانات**
   - تحقق من معلومات الاتصال في .env
   - تأكد من أن قاعدة البيانات موجودة
   - تحقق من صلاحيات المستخدم

3. **مشاكل في رفع الملفات**
   - تحقق من صلاحيات مجلد uploads
   - تحقق من حدود رفع الملفات في PHP

### ملفات السجلات:
- `error.log` - أخطاء التطبيق
- `/var/log/apache2/error.log` - أخطاء Apache
- `/var/log/nginx/error.log` - أخطاء Nginx

## الصيانة والتحديث

### النسخ الاحتياطي:
1. نسخ احتياطي لقاعدة البيانات يومياً
2. نسخ احتياطي للملفات أسبوعياً
3. اختبار استعادة النسخ الاحتياطية

### المراقبة:
- مراقبة استخدام الموارد
- مراقبة أداء قاعدة البيانات
- مراقبة ملفات السجلات

## الدعم الفني

للحصول على المساعدة:
- البريد الإلكتروني: <EMAIL>
- الوثائق: https://docs.wardena.app
- المجتمع: https://community.wardena.app
