<!--start content-->
<main class="page-content">
    <!--breadcrumb-->
    <div class="page-breadcrumb d-none d-sm-flex align-items-center mb-3">
        <a class="breadcrumb-title pe-3" href="/settings">الاعدادات</a>
        <div class="ps-3">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb mb-0 p-0">
                    <li class="breadcrumb-item"><a href="javascript:;"><i class="bx bx-home-alt"></i></a>
                    </li>
                    <li class="breadcrumb-item active" aria-current="page">اعدادات مالية</li>
                </ol>
            </nav>
        </div>
    </div>
    <!--end breadcrumb-->
    <div class="card">
        <div class="card-header py-3">
            <h6 class="mb-0">عمولة التطبيق</h6>
        </div>
        <div class="card-body">
            <select class="form-select mb-3" aria-label="Default select example" id="ourFee">
                <option value="0" <?php if ($resultFee == 0) {
                                        echo 'selected';
                                    } ?>>0 د.ع</option>
                <option value="1000" <?php if ($resultFee == 1000) {
                                        echo 'selected';
                                    } ?>>1000 د.ع</option>
                <option value="2000" <?php if ($resultFee == 2000) {
                                        echo 'selected';
                                    } ?>>2000 د.ع</option>
                <option value="3000" <?php if ($resultFee == 3000) {
                                        echo 'selected';
                                    } ?>>3000 د.ع</option>
                <option value="4000" <?php if ($resultFee == 4000) {
                                        echo 'selected';
                                    } ?>>4000 د.ع</option>
                <option value="5000" <?php if ($resultFee == 5000) {
                                        echo 'selected';
                                    } ?>>5000 د.ع</option>
                <option value="6000" <?php if ($resultFee == 6000) {
                                        echo 'selected';
                                    } ?>>6000 د.ع</option>
                <option value="7000" <?php if ($resultFee == 7000) {
                                        echo 'selected';
                                    } ?>>7000 د.ع</option>
                <option value="8000" <?php if ($resultFee == 8000) {
                                        echo 'selected';
                                    } ?>>8000 د.ع</option>
                <option value="9000" <?php if ($resultFee == 9000) {
                                        echo 'selected';
                                    } ?>>9000 د.ع</option>
                <option value="10000" <?php if ($resultFee == 10000) {
                                            echo 'selected';
                                        } ?>>10000 د.ع</option>
            </select>
        </div>
    </div>
    <div class="card">
        <div class="card-header py-3">
            <h6 class="mb-0">عمولات التوصيل</h6>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-12 col-lg-4 d-flex">
                    <div class="card border shadow-none w-100">
                        <div class="card-body">
                            <form class="row g-3" method="post" action="/delivery/new">
                                <div class="col-12">
                                    <label class="form-label">المدينة</label>
                                    <input type="text" name="city" class="form-control" placeholder="اسم المدينة او المحافظة">
                                </div>
                                <div class="col-12">
                                    <label class="form-label">المبلغ</label>
                                    <input type="number" name="fee" class="form-control" placeholder="مبلغ العمولة">
                                </div>
                                
                                <div class="col-12">
                                    <div class="d-grid">
                                        <button type="submit" class="btn btn-primary">اضافة</button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
                <div class="col-12 col-lg-8 d-flex">
                    <div class="card border shadow-none w-100">
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table align-middle">
                                    <thead class="table-light">
                                        <tr>
                                            <th>الرقم</th>
                                            <th>المدينة</th>
                                            <th>العمولة</th>
                                            <th>الخيارات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($result as $rate) { ?>

                                            <tr>
                                                <td>#<?php echo $rate['id'] ?></td>
                                                <td><?php echo $rate['city'] ?></td>
                                                <td><?php echo $rate['fee'] ?> IQD</td>
                                                <td>
                                                    <div class="d-flex align-items-center gap-3 fs-6">
                                                        <a href="delivery/edit?id=<?php echo $rate['id'] ?>" class="text-primary" data-bs-toggle="modal" data-bs-target="#modal-<?php echo $rate['id'] ?>" data-bs-placement="bottom" title="" data-bs-original-title="Edit info" aria-label="Edit"><i class="bi bi-pencil-fill"></i></a>
                                                        <a href="delete?id=<?php echo $rate['id'] ?>" class="text-danger" data-bs-toggle="tooltip" data-bs-placement="bottom" title="" data-bs-original-title="Delete" aria-label="Delete"><i class="bi bi-trash-fill"></i></a>
                                                    </div>
                                                </td>
                                            </tr>
                                            <!-- Modal -->
                                            <div class="modal fade" id="modal-<?php echo $rate['id'] ?>" tabindex="-1" aria-hidden="true">
                                                <div class="modal-dialog modal-lg modal-dialog-centered">
                                                    <div class="modal-content bg-primary">
                                                        <div class="modal-header">
                                                            <h5 class="modal-title text-white">تعديل العمولة</h5>
                                                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                                        </div>
                                                        <div class="modal-body text-white">
                                                            <div class="card-body">
                                                                <form class="row g-3" method="post" action="/delivery/update?id=<?php echo $rate['id'] ?>" enctype="multipart/form-data">
                                                                    <div class="col-12">
                                                                        <label class="form-label">المدينة</label>
                                                                        <input type="text" name="city" class="form-control" value="<?php echo $rate['city'] ?>">
                                                                    </div>
                                                                    <div class="col-12">
                                                                        <label class="form-label">النسبة</label>
                                                                        <input type="number" name="fee" class="form-control" value="<?php echo $rate['fee'] ?>">
                                                                    </div>
                                                                    <div class="col-12">
                                                                        <div class="d-flex modal-footer">
                                                                            <button type="submit" class="btn btn-primary" data-bs-dismiss="modal">نعديل</button>
                                                                            <button class="btn btn-primary" data-bs-dismiss="modal">اغلاق</button>
                                                                        </div>
                                                                    </div>
                                                                </form>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                        <?php } ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div><!--end row-->
        </div>
    </div>

</main>
<!--end page main-->
<script>
    document.addEventListener("DOMContentLoaded", function() {
        const selectElement = document.getElementById("ourFee");

        selectElement.addEventListener("change", function() {
            const selectedFee = this.value;

            fetch("/settings/updateOurFee", {
                method: "POST",
                headers: {
                    "Content-Type": "application/x-www-form-urlencoded",
                },
                body: `fee=${encodeURIComponent(selectedFee)}`,
                })
                .then(response => {
                    if (!response.ok) {
                        throw new Error("Failed to update fee");
                    }
                    return response.json();
                })
                .then(data => {
                    console.log("Fee updated successfully:", data.message);
                })
                .catch(error => {
                    console.error("Error updating fee:", error);
                });
        });
    });
</script>