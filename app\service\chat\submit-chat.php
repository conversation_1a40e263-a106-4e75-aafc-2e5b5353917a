<?php
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: POST");
header("Access-Control-Allow-Headers: Content-Type");

$uploadDir = 'uploads/'; // Directory for saving files
$dbFile = 'chat.json'; // File to store chat database

if ($_SERVER['REQUEST_METHOD'] === 'POST') {

    if (!isset($_FILES['file']) && !isset($_POST['content'])) {
        http_response_code(400);
        echo json_encode(["error" => "Invalid request method"]);
        exit;
    }

    if (isset($_POST['content'])) {
        if (!isset($_POST['sessionId']) || !isset($_POST['sender']) || !isset($_POST['type'])) {
            http_response_code(400);
            echo json_encode(["error" => "something is not set"]);
            exit;
        }
        $content = $_POST['content'];
    }

    if (isset($_FILES['file'])) {

        if (!isset($_FILES['file']) || !isset($_POST['sessionId']) || !isset($_POST['sender']) || !isset($_POST['type'])) {
            http_response_code(400);
            echo json_encode(["error" => "Invalid request"]);
            exit;
        }



        if (!is_dir($uploadDir)) {
            mkdir($uploadDir, 0755, true);
        }

        $file = $_FILES['file'];
        $extension = pathinfo($file['name'], PATHINFO_EXTENSION); 
        $uniqueName = uniqid() . '.' . $extension; 
        $filePath = $uploadDir . $uniqueName;

        $allowedTypes = ['image/jpeg', 'image/png', 'audio/webm'];
        if (!in_array($file['type'], $allowedTypes)) {
            http_response_code(400);
            echo json_encode(["error" => "Invalid file type"]);
            exit;
        }

        $maxSize = 5 * 1024 * 1024; // 5 MB
        if ($file['size'] > $maxSize) {
            http_response_code(400);
            echo json_encode(["error" => "File size exceeds limit"]);
            exit;
        }

        // Move uploaded file
        if (!move_uploaded_file($file['tmp_name'], $filePath)) {
            http_response_code(500);
            echo json_encode(["error" => "Failed to save file"]);
            exit;
        }

        $content = 'https://sinaae.com/admin/' . $filePath;

    }

    $messageEntry = [
        "messageId" => uniqid("msg-"),
        "sender" => $_POST['sender'],
        "timestamp" => date("c"), 
        "type" => $_POST['type'], 
        "content" => $content
    ];

    // Load existing database or create a new one
    $chatDatabase = file_exists($dbFile) ? json_decode(file_get_contents($dbFile), true) : ["chatSessions" => []];

    // Find the session or create a new one
    $sessionId = $_POST['sessionId'];
    $sessionKey = array_search($sessionId, array_column($chatDatabase['chatSessions'], 'sessionId'));

    if ($sessionKey === false) {
        $chatDatabase['chatSessions'][] = [
            "sessionId" => $sessionId,
            "userName" => $_POST['sender'],
            "startTime" => date("c"),
            "unreadCount" => 1,
            "messages" => [$messageEntry]
        ];
    } else {
        $chatDatabase['chatSessions'][$sessionKey]['unreadCount'] += 1;
        $chatDatabase['chatSessions'][$sessionKey]['messages'][] = $messageEntry;
    }

    // Save updated database
    file_put_contents($dbFile, json_encode($chatDatabase, JSON_PRETTY_PRINT));

    // Return response
    echo json_encode(["message" => "File uploaded and message saved", "url" => $messageEntry["content"]]);
} else {
    http_response_code(405);
    echo json_encode(["error" => "Invalid request method"]);
}

?>
