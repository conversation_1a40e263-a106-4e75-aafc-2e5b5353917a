<?php

class OrderService
{
    private $orders;
    private $details;

    private $products;
    private $stores;
    private $brands;

    private $users;

    public function __construct()
    {
        $this->orders = new CrudModel('orders');
        $this->details = new CrudModel('order_details');

        $this->products = new CrudModel('products');

        $this->stores = new CrudModel('stores');
        $this->brands = new CrudModel('brands');

        $this->users = new CrudModel('users');
    }

    public function createOrder($orderData){
        $order = [
            'customer_id' => $orderData['customer_id'],
            'store_id' => 0,
            'total_amount' => $orderData['total_amount'],
            'delivery_fee' => $orderData['delivery_fee'],
        ];
        
        $orderDetails = [];

        foreach ($orderData['cart'] as $productId => $product) {

            $name = $product['name'];
            $price = $product['price'];
            $quantity = $product['quantity'];
            $total_price = $quantity * $price;

            $orderDetails[] = [
                'order_id' => $orderId,
                'product_id' => $productId,
                'store_id' => 0,
                'quantity' => $quantity,
                'price' => $price,
                'total_price' => $total_price
            ];
        
            echo "Product ID: $productId\n";
            echo "Name: $name\n";
            echo "Price: $price\n";
            echo "Quantity: $quantity\n";
            echo "Image URL: $image\n";
            echo "--------------------------\n";
        }

        $order = $this->service->createOrderDetails($orderDetails);
        if ($order) {
            $this->logError('Order placed successfully.');
            $this->model->commit();

            http_response_code(200);
            echo json_encode(['message' => 'Order placed successfully.', 'orderId' => $orderId]);

        }
    }
    
    public function getOrder($id)
    {
        $order = $this->orders->getElement($id);

        $status_classes = [
            'pending' => 'bg-light-warning',
            'accepted' => 'bg-light-primary',
            'in_transit' => 'bg-light-info',
            'delivered' => 'bg-light-success',
            'canceled' => 'bg-light-danger'
        ];
    
        $status_arabic = [
            'pending' => 'قيد الانتظار',
            'accepted' => 'قيد التحضير',
            'in_transit' => 'في الطريق',
            'delivered' => 'تم التوصيل',
            'canceled' => 'ملغي'
        ];

        $details = $this->details->getElementsWhere('order_id', $id);

        $products = [];
        $total_app_earnings = 0;
        $total_store_earnings = 0;

        foreach ($details as $detail) {
            $product = $this->products->getElement($detail['product_id']);
            $store = $this->stores->getElement($product['store_id']);
            $brand = $this->brands->getElement($product['brand_id']);

            $app_earnings = $detail['total_price'] * $product['app_percentage'];
            $store_earnings = $detail['total_price'] - $app_earnings;

            $total_app_earnings += $app_earnings;
            $total_store_earnings += $store_earnings;

            $products[] = [
                'name' => $product['name'],
                'img' => $product['image'],
                'store_id' => $product['store_id'],
                'store' => $store['name'],
                'brand' => $brand['name'],
                'percentage' => $product['app_percentage'],
                'qty' => $detail['quantity'],
                'price' => $detail['price'],
                'total' => $detail['total_price'],
                'app_earnings' => $app_earnings,
                'store_earnings' => $store_earnings,
            ];
        }
        $customer = $this->users->getElement($order['customer_id']);
        $delivery = $this->users->getElement($order['delivery_boy_id']);

        $data = [
            'id' => $order['id'],
            'products' => $products,
            'customer_id' => $order['customer_id'],
            'customer_name' => $customer['name'],
            'customer_last_name' => $customer['last_name'],
            'customer_phone' => $customer['phone'],
            'address' => $customer['country']. ' / ' . $customer['state']. ' - ' . $customer['city']. ', ' . $customer['address'],
            'delivery_id' => $order['delivery_boy_id'],
            'delivery_name' => $delivery['name'] ?? 'غير محدد',
            'total_amount' => $order['total_amount'],
            'total_app_earnings' => $total_app_earnings,
            'total_store_earnings' => $total_store_earnings,
            'delivery_fee' => $order['delivery_fee'],
            'status' => [
                'english' => $order['status'],
                'arabic' => $status_arabic[$order['status']] ?? 'غير معروف',
                'class' => $status_classes[$order['status']] ?? 'text-secondary'
            ],
            'date' => $order['created_at']
        ];
        return $data;
    }
    
    public function getOrders()
    {
        $orders = $this->orders->getElements();
    
        $status_classes = [
            'pending' => 'bg-light-warning',
            'accepted' => 'bg-light-primary',
            'in_transit' => 'bg-light-info',
            'delivered' => 'bg-light-success',
            'canceled' => 'bg-light-danger'
        ];
    
        $status_arabic = [
            'pending' => 'قيد الانتظار',
            'accepted' => 'قيد التحضير',
            'in_transit' => 'في الطريق',
            'delivered' => 'تم التوصيل',
            'canceled' => 'ملغي'
        ];
    
        $data = [];
    
        foreach ($orders as $order) {
    
            $details = $this->details->getElementsWhere('order_id', $order['id']);
            $products = [];
            $total_app_earnings = 0;
            $total_store_earnings = 0;
    
            foreach ($details as $detail) {
                $product = $this->products->getElement($detail['product_id']);
                $store = $this->stores->getElement($product['store_id']);
                $brand = $this->brands->getElement($product['brand_id']);
    
                $app_earnings = $detail['total_price'] * $product['app_percentage'];
                $store_earnings = $detail['total_price'] - $app_earnings;
    
                $total_app_earnings += $app_earnings;
                $total_store_earnings += $store_earnings;
    
                $products[] = [
                    'name' => $product['name'],
                    'img' => $product['image'],
                    'store' => $store['name'],
                    'brand' => $brand['name'],
                    'percentage' => $product['app_percentage'],
                    'qty' => $detail['quantity'],
                    'price' => $detail['price'],
                    'total' => $detail['total_price'],
                    'app_earnings' => $app_earnings,
                    'store_earnings' => $store_earnings,
                ];
            }
            $customer = $this->users->getElement($order['customer_id']);
            $delivery = $this->users->getElement($order['delivery_boy_id']);
    
            $data[] = [
                'id' => $order['id'],
                'products' => $products,
                'customer_id' => $order['customer_id'],
                'customer_name' => $customer['name'],
                'customer_last_name' => $customer['last_name'],
                'customer_phone' => $customer['phone'],
                'address' => $customer['country']. ' / ' . $customer['state']. ' - ' . $customer['city']. ', ' . $customer['address'],
                'delivery_id' => $order['delivery_boy_id'],
                'delivery_name' => $delivery['name'] ?? 'غير محدد',
                'total_amount' => $order['total_amount'],
                'total_app_earnings' => $total_app_earnings,
                'total_store_earnings' => $total_store_earnings,
                'delivery_fee' => $order['delivery_fee'],
                'status' => [
                    'english' => $order['status'],
                    'arabic' => $status_arabic[$order['status']] ?? 'غير معروف',
                    'class' => $status_classes[$order['status']] ?? 'text-secondary'
                ],
                'date' => $order['created_at']
            ];
        }
    
        return $data;
    }    

    public function getStoreOrder($id, $storeID)
    {
        $order = $this->orders->getElement($id);

        $status_classes = [
            'pending' => 'bg-light-warning',
            'accepted' => 'bg-light-primary',
            'in_transit' => 'bg-light-info',
            'delivered' => 'bg-light-success',
            'canceled' => 'bg-light-danger'
        ];
    
        $status_arabic = [
            'pending' => 'قيد الانتظار',
            'accepted' => 'قيد التحضير',
            'in_transit' => 'في الطريق',
            'delivered' => 'تم التوصيل',
            'canceled' => 'ملغي'
        ];

        $details = $this->details->getElementsWhere('order_id', $id);

        $products = [];
        $total_app_earnings = 0;
        $total_store_earnings = 0;
        $total = 0;

        foreach ($details as $detail) {
            $product = $this->products->getElement($detail['product_id']);
            if ($product['store_id'] == $storeID) {

                $store = $this->stores->getElement($product['store_id']);
                $brand = $this->brands->getElement($product['brand_id']);

                $app_earnings = $detail['total_price'] * $product['app_percentage'];
                $store_earnings = $detail['total_price'] - $app_earnings;

                $total_app_earnings += $app_earnings;
                $total_store_earnings += $store_earnings;

                $products[] = [
                    'name' => $product['name'],
                    'img' => $product['image'],
                    'store_id' => $product['store_id'],
                    'store' => $store['name'],
                    'brand' => $brand['name'],
                    'percentage' => $product['app_percentage'],
                    'qty' => $detail['quantity'],
                    'price' => $detail['price'],
                    'total' => $detail['total_price'],
                    'app_earnings' => $app_earnings,
                    'store_earnings' => $store_earnings,
                ];

                $total += $detail['total_price'];
            }
        }
        $customer = $this->users->getElement($order['customer_id']);
        $delivery = $this->users->getElement($order['delivery_boy_id']);

        $data = [
            'id' => $order['id'],
            'products' => $products,
            'customer_id' => $order['customer_id'],
            'customer_name' => $customer['name'],
            'customer_last_name' => $customer['last_name'],
            'customer_phone' => $customer['phone'],
            'address' => $customer['country']. ' / ' . $customer['state']. ' - ' . $customer['city']. ', ' . $customer['address'],
            'delivery_id' => $order['delivery_boy_id'],
            'delivery_name' => $delivery['name'] ?? 'غير محدد',
            'total_amount' => $total,
            'total_app_earnings' => $total_app_earnings,
            'total_store_earnings' => $total_store_earnings,
            'delivery_fee' => $order['delivery_fee'],
            'status' => [
                'english' => $order['status'],
                'arabic' => $status_arabic[$order['status']] ?? 'غير معروف',
                'class' => $status_classes[$order['status']] ?? 'text-secondary'
            ],
            'date' => $order['created_at']
        ];
        return $data;
    }

    public function getDeliveryOrders($delivery)
    {

        $orders = $this->orders->getElementsWhere('delivery_boy_id', $delivery);
        $data = [];

        foreach ($orders as $order) {
            $data[] = $this->getOrder($order['id']);
        }
        return $data;
    }

        
    public function getStoreOrders($store_id)
    {

        $details = $this->details->getElements();
        $data = [];

        foreach ($details as $detail) {
            $product = $this->products->getElement($detail['product_id']);
            if($product['store_id'] == $store_id){
                $data[] = $this->getStoreOrder($detail['order_id'], $store_id);
            }
        }
        return $data;
    }

    public function getClientOrders($customer_id)
    {
        $orders = $this->orders->getElementsWhere('customer_id', $customer_id);
        
        $data = [];

        foreach ($orders as $order) {

            $details = $this->details->getElementsWhere('order_id', $order['id']);
            $products = [];
            foreach ($details as $detail) {
                $product = $this->products->getElement($detail['product_id']);
                $store = $this->stores->getElement($product['store_id']);
                $brand = $this->brands->getElement($product['brand_id']);
    
                $products[] = [
                    'name' => $product['name'],
                    'img' => $product['image'],
                    'store' => $store['name'],
                    'brand' => $brand['name'],
                    'percentage' => $product['app_percentage'],
                    'qty' => $detail['quantity'],
                    'price' => $detail['price'],
                    'total' => $detail['total_price'],
                ];
            }
            $customer = $this->users->getElement($order['customer_id']);
            $delivery = $this->users->getElement($order['delivery_boy_id']);
    
    
            $data[] = [
                'id' => $order['id'],
                'products' => $products,
                'customer_id' => $order['customer_id'],
                'customer_name' => $customer['name'],
                'customer_last_name' => $customer['last_name'],
                'customer_phone' => $customer['phone'],
                'address' => $customer['country']. ' / ' . $customer['state']. ' - ' . $customer['city']. ', ' . $customer['address'],
                'delivery_id' => $order['delivery_boy_id'],
                'delivery_name' => $delivery['name'],
                'total_amount' => $order['totla_amount'],
                'app_earnings' => $order['app_earnings'],
                'store_earnings' => $order['store_earnings'],
                'delivery_fee' => $order['delivery_fee'],
                'status' => $order['status'],
            ];
        }

        return $data;
    }

}
