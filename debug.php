<?php
/**
 * Wardena API - Debug Script
 * This script helps diagnose common issues
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);

echo "<h1>🔧 Wardena API Debug Tool</h1>";
echo "<style>body{font-family:Arial;margin:20px;} .ok{color:green;} .error{color:red;} .warning{color:orange;} pre{background:#f5f5f5;padding:10px;border-radius:5px;}</style>";

// Test 1: PHP Version
echo "<h2>1. PHP Version Check</h2>";
$phpVersion = phpversion();
echo "PHP Version: <strong>{$phpVersion}</strong> ";
if (version_compare($phpVersion, '7.4.0', '>=')) {
    echo "<span class='ok'>✅ OK</span><br>";
} else {
    echo "<span class='error'>❌ Too Old (Need 7.4+)</span><br>";
}

// Test 2: Required Extensions
echo "<h2>2. PHP Extensions Check</h2>";
$requiredExtensions = ['pdo', 'pdo_mysql', 'json', 'openssl', 'curl', 'fileinfo', 'mbstring'];
foreach ($requiredExtensions as $ext) {
    echo "{$ext}: ";
    if (extension_loaded($ext)) {
        echo "<span class='ok'>✅ Loaded</span><br>";
    } else {
        echo "<span class='error'>❌ Missing</span><br>";
    }
}

// Test 3: File Permissions
echo "<h2>3. File Permissions Check</h2>";
$directories = [
    'public/uploads/' => 'Uploads directory',
    'storage/' => 'Storage directory',
    'config/' => 'Config directory',
    '.env' => 'Environment file'
];

foreach ($directories as $path => $description) {
    echo "{$description}: ";
    if (file_exists($path)) {
        if (is_dir($path)) {
            if (is_writable($path)) {
                echo "<span class='ok'>✅ Writable</span><br>";
            } else {
                echo "<span class='warning'>⚠️ Not Writable</span><br>";
            }
        } else {
            if (is_readable($path)) {
                echo "<span class='ok'>✅ Readable</span><br>";
            } else {
                echo "<span class='error'>❌ Not Readable</span><br>";
            }
        }
    } else {
        echo "<span class='error'>❌ Not Found</span><br>";
    }
}

// Test 4: .env File Check
echo "<h2>4. Environment Configuration</h2>";
if (file_exists('.env')) {
    echo ".env file: <span class='ok'>✅ Found</span><br>";
    
    // Try to load config
    try {
        require_once 'config/config.php';
        echo "Config loading: <span class='ok'>✅ Success</span><br>";
        
        // Check key constants
        $constants = ['DB_HOST', 'DB_NAME', 'DB_USER', 'BASE_URL', 'SECRET_KEY'];
        foreach ($constants as $const) {
            if (defined($const)) {
                $value = constant($const);
                echo "{$const}: <span class='ok'>✅ Defined</span> ";
                if (!empty($value)) {
                    echo "(Value: " . substr($value, 0, 20) . "...)<br>";
                } else {
                    echo "<span class='warning'>⚠️ Empty</span><br>";
                }
            } else {
                echo "{$const}: <span class='error'>❌ Not Defined</span><br>";
            }
        }
        
    } catch (Exception $e) {
        echo "Config loading: <span class='error'>❌ Failed</span><br>";
        echo "Error: <pre>" . htmlspecialchars($e->getMessage()) . "</pre>";
    }
} else {
    echo ".env file: <span class='error'>❌ Missing</span><br>";
    echo "<p><strong>Solution:</strong> Create .env file with database configuration</p>";
}

// Test 5: Database Connection
echo "<h2>5. Database Connection</h2>";
if (defined('DB_HOST') && defined('DB_NAME') && defined('DB_USER') && defined('DB_PASS')) {
    try {
        $pdo = new PDO(
            "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4",
            DB_USER,
            DB_PASS,
            [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
        );
        echo "Database connection: <span class='ok'>✅ Success</span><br>";
        
        // Check tables
        $stmt = $pdo->query("SHOW TABLES");
        $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
        echo "Tables found: <strong>" . count($tables) . "</strong><br>";
        
        if (count($tables) == 0) {
            echo "<span class='warning'>⚠️ No tables found</span><br>";
            echo "<p><strong>Solution:</strong> Run database setup</p>";
        }
        
    } catch (PDOException $e) {
        echo "Database connection: <span class='error'>❌ Failed</span><br>";
        echo "Error: <pre>" . htmlspecialchars($e->getMessage()) . "</pre>";
        echo "<p><strong>Solutions:</strong></p>";
        echo "<ul>";
        echo "<li>Check if MySQL is running in XAMPP</li>";
        echo "<li>Verify database credentials in .env</li>";
        echo "<li>Create database if it doesn't exist</li>";
        echo "</ul>";
    }
} else {
    echo "Database configuration: <span class='error'>❌ Missing</span><br>";
}

// Test 6: Apache Modules
echo "<h2>6. Apache Modules</h2>";
if (function_exists('apache_get_modules')) {
    $modules = apache_get_modules();
    $requiredModules = ['mod_rewrite', 'mod_headers'];
    
    foreach ($requiredModules as $module) {
        echo "{$module}: ";
        if (in_array($module, $modules)) {
            echo "<span class='ok'>✅ Loaded</span><br>";
        } else {
            echo "<span class='error'>❌ Missing</span><br>";
        }
    }
} else {
    echo "Cannot check Apache modules (not running under Apache or function not available)<br>";
}

// Test 7: .htaccess Files
echo "<h2>7. .htaccess Files</h2>";
$htaccessFiles = ['.htaccess', 'public/.htaccess'];
foreach ($htaccessFiles as $file) {
    echo "{$file}: ";
    if (file_exists($file)) {
        echo "<span class='ok'>✅ Found</span><br>";
    } else {
        echo "<span class='error'>❌ Missing</span><br>";
    }
}

// Test 8: Error Logs
echo "<h2>8. Recent Error Logs</h2>";
$errorLogFiles = ['error.log', '../logs/error.log', 'C:/xampp/apache/logs/error.log'];
$foundLogs = false;

foreach ($errorLogFiles as $logFile) {
    if (file_exists($logFile) && is_readable($logFile)) {
        $foundLogs = true;
        echo "<h3>Log: {$logFile}</h3>";
        $lines = file($logFile);
        $recentLines = array_slice($lines, -10); // Last 10 lines
        echo "<pre>" . htmlspecialchars(implode('', $recentLines)) . "</pre>";
        break;
    }
}

if (!$foundLogs) {
    echo "No accessible error logs found<br>";
}

// Test 9: Quick Fixes
echo "<h2>9. Quick Fixes</h2>";
echo "<h3>Common Solutions:</h3>";
echo "<ol>";
echo "<li><strong>If .env is missing:</strong> Copy from .env.example and configure</li>";
echo "<li><strong>If database connection fails:</strong> Start MySQL in XAMPP and create database</li>";
echo "<li><strong>If mod_rewrite missing:</strong> Enable in Apache configuration</li>";
echo "<li><strong>If permission errors:</strong> Set proper folder permissions</li>";
echo "<li><strong>If tables missing:</strong> Run <a href='quick-setup.php'>quick-setup.php</a></li>";
echo "</ol>";

echo "<h3>Quick Actions:</h3>";
echo "<p>";
echo "<a href='quick-setup.php' style='background:#007cba;color:white;padding:10px;text-decoration:none;border-radius:5px;'>🚀 Run Quick Setup</a> ";
echo "<a href='test-setup.php' style='background:#28a745;color:white;padding:10px;text-decoration:none;border-radius:5px;'>🧪 Run System Test</a> ";
echo "<a href='config/database-setup.php' style='background:#ffc107;color:black;padding:10px;text-decoration:none;border-radius:5px;'>🗄️ Setup Database</a>";
echo "</p>";

// Test 10: System Info
echo "<h2>10. System Information</h2>";
echo "<pre>";
echo "PHP Version: " . phpversion() . "\n";
echo "Server Software: " . ($_SERVER['SERVER_SOFTWARE'] ?? 'Unknown') . "\n";
echo "Document Root: " . ($_SERVER['DOCUMENT_ROOT'] ?? 'Unknown') . "\n";
echo "Script Path: " . __FILE__ . "\n";
echo "Current Directory: " . getcwd() . "\n";
echo "Memory Limit: " . ini_get('memory_limit') . "\n";
echo "Max Execution Time: " . ini_get('max_execution_time') . "\n";
echo "Upload Max Filesize: " . ini_get('upload_max_filesize') . "\n";
echo "Post Max Size: " . ini_get('post_max_size') . "\n";
echo "</pre>";

echo "<hr>";
echo "<p><strong>Debug completed!</strong> If you still have issues, check the solutions above or contact support.</p>";
?>
