<?php

class RegisterModel extends BaseModel
{

    public function __construct()
    {
        parent::__construct();
        $this->table = 'users';
    }

    public function register(array $userData)
    {

        $allowedFields = ['name', 'last_name', 'username', 'email', 'password', 'phone', 'country', 'city', 'state', 'address', 'postal_code', 'role'];
        $userData = array_intersect_key($userData, array_flip($allowedFields));

        return $this->insertReturnId($userData);
    }

    public function registerEmployee(array $userData)
    {

        $allowedFields = ['user_id', 'gender', 'birthday', 'position', 'salary', 'start_date', 'end_date', 'education', 'institute', 'specialization', 'division'];
        $userData = array_intersect_key($userData, array_flip($allowedFields));

        // Temporarily change the table for this operation
        $originalTable = $this->table;
        $this->table = 'employees';

        $employee = $this->insert($userData);

        $this->table = $originalTable; // Restore the original table

        return $employee;
    }

    public function usernameExists($username)
    {
        return $this->exists('username', $username);
    }

    public function emailExists($email)
    {
        return $this->exists('email', $email);
    }

    public function getContacts()
    {
        return $this->getAll();
    }

    public function getEmployees()
    {
        // Temporarily change the table for this operation
        $originalTable = $this->table;
        $this->table = 'employees';
        $services = $this->getAll();
        $this->table = $originalTable; // Restore the original table
        return $services;
    }
}
