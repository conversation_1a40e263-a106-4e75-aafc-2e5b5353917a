<?php
// Function to load .env file into environment variables
function loadEnv($path)
{
    if (!file_exists($path)) {
        throw new \Exception('.env file not found');
    }

    $lines = file($path, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos(trim($line), '#') === 0) {
            continue;
        }

        list($name, $value) = explode('=', $line, 2);
        $name = trim($name);
        $value = trim($value);

        if (!array_key_exists($name, $_SERVER) && !array_key_exists($name, $_ENV)) {
            putenv("$name=$value");
            $_ENV[$name] = $value;
            $_SERVER[$name] = $value;
        }
    }
}

// Load .env file
loadEnv(__DIR__ . '/../.env');

// Application environment: 'development', 'production', etc.
define('APP_ENV', getenv('APP_ENV'));
define('SECRET_KEY', getenv('SECRET'));

// Database settings
define('DB_HOST', getenv('DB_HOST') ?: 'localhost');
define('DB_NAME', getenv('DB_NAME') ?: 'wardena_db');
define('DB_USER', getenv('DB_USER') ?: 'root');
define('DB_PASS', getenv('DB_PASS') ?: '');
define('DB_CHARSET', getenv('DB_CHARSET') ?: 'utf8mb4');
define('DB_PORT', getenv('DB_PORT') ?: '3306');

// Base URL (useful for generating links in your application)
define('BASE_URL', getenv('URL') ?: 'http://localhost/');
define('API_URL', getenv('API_URL') ?: BASE_URL . 'api/');

// Application settings
define('APP_NAME', getenv('APP_NAME') ?: 'Wardena');
define('APP_VERSION', getenv('APP_VERSION') ?: '1.0.0');

// Security settings
define('JWT_SECRET', getenv('JWT_SECRET') ?: SECRET_KEY);
define('SESSION_LIFETIME', getenv('SESSION_LIFETIME') ?: 86400);
define('PASSWORD_RESET_EXPIRY', getenv('PASSWORD_RESET_EXPIRY') ?: 3600);

// Email settings
define('MAIL_HOST', getenv('MAIL_HOST') ?: 'smtp.gmail.com');
define('MAIL_PORT', getenv('MAIL_PORT') ?: 587);
define('MAIL_USERNAME', getenv('MAIL_USERNAME') ?: '');
define('MAIL_PASSWORD', getenv('MAIL_PASSWORD') ?: '');
define('MAIL_ENCRYPTION', getenv('MAIL_ENCRYPTION') ?: 'tls');
define('MAIL_FROM_ADDRESS', getenv('MAIL_FROM_ADDRESS') ?: '<EMAIL>');
define('MAIL_FROM_NAME', getenv('MAIL_FROM_NAME') ?: APP_NAME);

// File upload settings
define('MAX_FILE_SIZE', getenv('MAX_FILE_SIZE') ?: 10485760); // 10MB
define('ALLOWED_FILE_TYPES', getenv('ALLOWED_FILE_TYPES') ?: 'jpg,jpeg,png,gif,pdf,doc,docx');
define('UPLOAD_PATH', getenv('UPLOAD_PATH') ?: 'public/uploads/');

// API settings
define('RATE_LIMIT_REQUESTS', getenv('RATE_LIMIT_REQUESTS') ?: 100);
define('RATE_LIMIT_WINDOW', getenv('RATE_LIMIT_WINDOW') ?: 3600);

// Cache settings
define('CACHE_ENABLED', getenv('CACHE_ENABLED') === 'true');
define('CACHE_DRIVER', getenv('CACHE_DRIVER') ?: 'file');
define('CACHE_TTL', getenv('CACHE_TTL') ?: 3600);

// Logging settings
define('LOG_LEVEL', getenv('LOG_LEVEL') ?: 'error');
define('LOG_FILE', getenv('LOG_FILE') ?: 'error.log');

// External services
define('GOOGLE_MAPS_API_KEY', getenv('GOOGLE_MAPS_API_KEY') ?: '');
define('FIREBASE_SERVER_KEY', getenv('FIREBASE_SERVER_KEY') ?: '');
define('PAYMENT_GATEWAY_KEY', getenv('PAYMENT_GATEWAY_KEY') ?: '');

// Timezone setting
date_default_timezone_set('UTC');

// Error reporting based on environment
if (APP_ENV === 'development') {
    // Enable error reporting for debugging
    ini_set('display_errors', 1);
    ini_set('display_startup_errors', 1);
    error_reporting(E_ALL);
} else {
    ini_set('display_errors', 0);
    error_reporting(0);
}

// Session settings
ini_set('session.cookie_httponly', 1); // Make session cookie HttpOnly
ini_set('session.gc_maxlifetime', 86400);
session_name('YOUR_SESSION_NAME'); // Custom session name for security
// Start session
session_start();

?>
