# ===================================
# Wardena API - Docker Configuration
# ===================================

# Application Settings
APP_ENV=development
APP_NAME=Wardena
APP_VERSION=1.0.0
URL=http://localhost:8080/
API_URL=http://localhost:8080/api/

# Security Settings
SECRET=docker-secret-key-change-in-production-12345678901234567890
JWT_SECRET=docker-jwt-secret-key-change-in-production-12345678901234567890
SESSION_LIFETIME=86400
PASSWORD_RESET_EXPIRY=3600

# Database Configuration (Docker)
DB_HOST=db
DB_NAME=wardena_db
DB_USER=wardena
DB_PASS=wardena123
DB_CHARSET=utf8mb4
DB_PORT=3306

# Email Configuration
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=
MAIL_PASSWORD=
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME=Wardena

# File Upload Settings
MAX_FILE_SIZE=10485760
ALLOWED_FILE_TYPES=jpg,jpeg,png,gif,pdf,doc,docx
UPLOAD_PATH=public/uploads/

# API Rate Limiting
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=3600

# Cache Settings
CACHE_ENABLED=true
CACHE_DRIVER=redis
CACHE_TTL=3600

# Redis Configuration
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=

# Logging
LOG_LEVEL=debug
LOG_FILE=error.log

# External Services
GOOGLE_MAPS_API_KEY=
FIREBASE_SERVER_KEY=
PAYMENT_GATEWAY_KEY=
