<?php

class SettingsController extends BaseController
{

    private $model;
    private $slide;
    private $users;
    private $auth;


    public function __construct($request, $response)
    {
        parent::__construct($request, $response);
        $this->model = new CrudModel('delivery_rates');
        $this->slide = new CrudModel('slides');
        $this->users = new CrudModel('users');
        $this->auth = new AuthService();

    }

    public function index()
    {
        
        if ($this->isLoggedIn()) {
            $userId = $this->getSessionData('user_id');
            $user = $this->users->getElement($userId);
            $result = $this->getOurFee();
            $data = [
                'title' => 'Settings',
                'style' => null,
                'user' => $user,
                'result' => $result
            ];
            $this->render('../app/view/admin/settings.php', $data);
        } else {
            $this->redirect('/login');
        }
    }

    public function rates()
    {
        if ($this->auth->admin() == true) {
            
                $userId = $this->getSessionData('user_id');
                $user = $this->users->getElement($userId);
                $result = $this->model->getElements();
                $resultFee = $this->getOurFee();
                $data = [
                    'title' => 'Delivery Rates',
                    'style' => null,
                    'user' => $user,
                    'result' => $result,
                    'resultFee' => $resultFee,
                ];
                $this->render('../app/view/admin/settings-rates.php', $data);

        } else {
            echo "error";
        }
    }

    public function slides()
    {
        if ($this->auth->admin() == true) {
            
                $userId = $this->getSessionData('user_id');
                $user = $this->users->getElement($userId);
                $result = $this->slide->getElements();
                $data = [
                    'title' => 'Delivery Rates',
                    'style' => null,
                    'user' => $user,
                    'result' => $result,
                ];
                $this->render('../app/view/admin/settings-slides.php', $data);

        } else {
            echo "error";
        }
    }

    function updateOurFee()
    {
        $filePath = '../config/settings.json';
        $newFee = $_POST['fee'];


        if (!file_exists($filePath)) {
            die("Error: File not found.");
        }

        $fileContent = file_get_contents($filePath);

        $data = json_decode($fileContent, true);

        if (json_last_error() !== JSON_ERROR_NONE) {
            die("Error: Invalid JSON format.");
        }

        $data['ourFee'] = $newFee;

        $newJsonContent = json_encode($data, JSON_PRETTY_PRINT);

        if (json_last_error() !== JSON_ERROR_NONE) {
            die("Error: Failed to encode JSON.");
        }

        if (file_put_contents($filePath, $newJsonContent) === false) {
            die("Error: Failed to write to the file.");
        }

    }

    function getOurFee() {
        $filePath = '../config/settings.json';
        if (!file_exists($filePath)) {
            die("Error: File not found.");
        }
        $fileContent = file_get_contents($filePath);
        $data = json_decode($fileContent, true);

        if (json_last_error() !== JSON_ERROR_NONE) {
            die("Error: Invalid JSON format.");
        }
        return $data['ourFee'] ?? null;
    }

}
