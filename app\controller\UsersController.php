<?php

class UsersController extends BaseController
{

    private $model;
    private $storeService;
    private $val;
    private $auth;


    public function __construct($request, $response)
    {
        parent::__construct($request, $response);
        $this->model = new CrudModel('users');
        $this->storeService = new StoreService();
        $this->val = new ValidateModel('users');
        $this->auth = new AuthService();

    }

    public function index()
    {
        if ($this->auth->admin() == true || $this->auth->employee() == true) {
            $userId = $this->getSessionData('user_id');
            $user = $this->model->getElement($userId);
            $admins = $this->model->getElementsWhere('role', 'admin');
            $role = 'admin';
            $data = [
                'title' => 'المدراء',
                'style' => null,
                'user' => $user,
                'admins' => $admins,
                'role' => $role,

            ];

            $this->render('../app/view/admin/user-management.php', $data);
        } else {
            $this->redirect('/login');
        }
    }

    public function edit()
    {
        if ($this->isLoggedIn()) {
            $id = $_GET['user'];
            $userId = $this->getSessionData('user_id');
            $user = $this->model->getElement($userId);
            $result = $this->model->getElement($id);

            $data = [
                'title' => 'تعديل المستخدمين',
                'style' => null,
                'user' => $user,
                'result' => $result,
            ];

            $this->render('../app/view/admin/edit-user.php', $data);
        } else {
            $this->redirect('/login');
        }
    }

    public function employees()
    {
        if ($this->auth->admin() == true || $this->auth->employee() == true) {
            $userId = $this->getSessionData('user_id');
            $user = $this->model->getElement($userId);
            $admins = $this->model->getElementsWhere('role', 'employee');
            $role = 'employee';
            $data = [
                'title' => 'الموظفين',
                'style' => null,
                'user' => $user,
                'admins' => $admins,
                'role' => $role,

            ];

            $this->render('../app/view/admin/employees.php', $data);
        } else {
            $this->redirect('/login');
        }
    }

    
    public function customers()
    {
        if ($this->isLoggedIn()) {
            $userId = $this->getSessionData('user_id');
            $user = $this->model->getElement($userId);
            $admins = $this->model->getElementsWhere('role', 'customer');
            $role = 'customer';
            $data = [
                'title' => 'العملاء',
                'style' => null,
                'user' => $user,
                'admins' => $admins,
                'role' => $role,

            ];

            $this->render('../app/view/admin/customers.php', $data);
        } else {
            $this->redirect('/login');
        }
    }
    
    public function agents()
    {
        if ($this->isLoggedIn()) {
            $userId = $this->getSessionData('user_id');
            $user = $this->model->getElement($userId);
            $admins = $this->model->getElementsWhere('role', 'delivery_boy');
            $role = 'delivery_boy';
            $data = [
                'title' => 'مندوب التوصيل',
                'style' => null,
                'user' => $user,
                'admins' => $admins,
                'role' => $role,

            ];
            $this->render('../app/view/admin/agents.php', $data);
        } else {
            $this->redirect('/login');
        }
    }

    public function newStore()
    {
        if ($this->isLoggedIn()) {
            if ($this->isPost()) {
                $this->handleStoreRegistration();
            } else {
                $userId = $this->getSessionData('user_id');
                $user = $this->model->getElement($userId);
                $data = [
                    'title' => 'متجر جديد',
                    'style' => null,
                    'user' => $user
                ];

                $this->render('../app/view/admin/new-store.php', $data);
            }
        } else {
            echo "error";
        }
    }

    public function newUser()
    {
        if ($this->isLoggedIn()) {
            if ($this->isPost()) {
                $role = $_POST['role'];
                $this->handleRegistration($role);
            } else {
                $userId = $this->getSessionData('user_id');
                $user = $this->model->getElement($userId);
                $role = $_GET['role'];
                $data = [
                    'title' => 'مستخدم جديد',
                    'style' => null,
                    'user' => $user,
                    'role' => $role,
                ];

                $this->render('../app/view/admin/new-user.php', $data);
            }
        } else {
            echo "error";
        }
    }

    private function handleStoreRegistration()
    {

        try {

            $this->model->beginTransaction();

            $fields = ['store', 'name', 'last_name', 'phone', 'country', 'state', 'city', 'address', 'email', 'username', 'password', 'confirm_password'];
            $formData = $this->getFormData($fields);
            $validationErrors = $this->validateStoreFormData($formData);

            if (!empty($validationErrors)) {
                $data = [
                    'errors' => $validationErrors,
                    'old' => $formData,
                ];
                $this->redirect('/users/new-store', $data);
                $this->logError('Failed to validate');

                return;
            }

            $hashedPassword = password_hash($formData['password'], PASSWORD_DEFAULT);
            $a['password'] = $hashedPassword;
            $a['role'] = 'store_owner';
            $a['name'] = $formData['name'];
            $a['last_name'] = $formData['last_name'];
            $a['phone'] = $formData['phone'];
            $a['country'] = $formData['country'];
            $a['state'] = $formData['state'];
            $a['city'] = $formData['city'];
            $a['address'] = $formData['address'];
            $a['email'] = $formData['email'];
            $a['username'] = $formData['username'];

            $userId = $this->model->createElementGetId($a);
            if (!$userId) {
                $this->logError('Failed to create user');
                $this->model->rollBack();

            } else {
                $storeData = [
                    'name' => $formData['store'],
                    'owner_id' => $userId,
                ];
                if (!$this->storeService->createStore($storeData)) {
                    $this->logError('Failed to create store');
                    $this->model->rollBack();
                } else {
                    $this->model->commit();
                    $with = ['result' => 'success'];
                    $this->redirect('/stores', $with);
                    return;
                }
            }
        } catch (Exception $e) {
            $this->model->rollBack();
            $this->logError($e->getMessage());
            return $this->redirect('/error');
        }
    }

    private function handleRegistration($role)
    {
        $redirect = '/users';
        if ($role == 'admin') {
            if (!$this->auth->admin() == true) {
                $this->redirect('/login');
                return;
            }
           $redirect = '/users';
        }
        elseif 
        ($role == 'employee') {
            if (!$this->auth->admin() == true) {
                $this->redirect('/login');
                return;
            }
            $redirect = '/users/employees';
        }
        elseif 
        ($role == 'customer') {
            $redirect = '/users/customers';
        }        elseif 
        ($role == 'delivery_boy') {
            $redirect = '/users/agents';
        }

        $fields = ['name', 'last_name', 'phone', 'country', 'state', 'city', 'address', 'email', 'username', 'password', 'confirm_password'];
        $formData = $this->getFormData($fields);
        $validationErrors = $this->validateFormData($formData);

        if (!empty($validationErrors)) {
            $data = [
                'errors' => $validationErrors,
                'old' => $formData,
            ];
            $this->redirect('/users/new-user', $data);
            $this->logError('Failed to validate');
            return;
        }

        $hashedPassword = password_hash($formData['password'], PASSWORD_DEFAULT);

        $a['password'] = $hashedPassword;
        $a['role'] = $role;
        $a['name'] = $formData['name'];
        $a['last_name'] = $formData['last_name'];
        $a['phone'] = $formData['phone'];
        $a['country'] = $formData['country'];
        $a['state'] = $formData['state'];
        $a['city'] = $formData['city'];
        $a['address'] = $formData['address'];
        $a['email'] = $formData['email'];
        $a['username'] = $formData['username'];

        if ($this->model->createElement($a)) {
            $this->setAlert('Registration successful!', 'success');
            $this->redirect($redirect);
        } else {
            $this->setAlert('There was a problem during registration. Please try again!', 'error');
        }
    }

    public function delete()
    {

        try {
            $this->model->beginTransaction();

            $role = $_POST['url'];
            $this->logError('faild to delete user redirect');
            $redirect = '/users';
            if ($role == 'admin') {
                if (!$this->auth->admin() == true) {
                    $this->redirect('/login');
                    return;
                }
               $redirect = '/users';
            }
            elseif ($role == 'employee') {
                if (!$this->auth->admin() == true) {
                    $this->redirect('/login');
                    return;
                }
                $redirect = '/users/employees';
            }
            elseif ($role == 'customer') {
                $redirect = '/users/customers';
            }       
            elseif ($role == 'delivery_boy') {
                $redirect = '/users/agents';
            }

            $id = $_POST['user'];

            if (!$this->model->deleteElement($id)) {
                $this->model->rollBack();
                $this->logError('faild to delete user' . $id . 'redirect' . $redirect);
                return false;
            } else {
                $this->model->commit();
                $this->redirect($redirect);
                return;
            }
        } catch (Exception $e) {
            $this->model->rollBack();
            $this->logError($e->getMessage());
            return $this->redirect('/error');
        }
    }

    
    public function updateStore()
    {
        try {
            $this->model->beginTransaction();

            $id = $_GET['id'];
            $store = $_GET['store_id'];

            $fields = ['name', 'last_name', 'phone', 'country', 'state', 'city', 'address'];
            $formData = $this->getFormData($fields);
            $validationErrors = $this->val->formData($formData);

            if (!empty($validationErrors)) {
                $data = [
                    'errors' => $validationErrors,
                    'old' => $formData,
                ];
                $this->redirect('/stores/edit?store='. $store, $data);
                $this->logError('Failed to validate');
                return;
            }

            if (!$this->model->updateElement($formData, $id)) {
                $this->model->rollBack();
                return false;
            } else {
                $this->model->commit();
                $this->redirect('/stores/edit?store='. $store);
                return;
            }
        } catch (Exception $e) {
            $this->model->rollBack();
            $this->logError($e->getMessage());
            return $this->redirect('/error');
        }
    }

    public function updateStorePassword(){
        try {
            $this->model->beginTransaction();

            $id = $_GET['id'];
            $store = $_GET['store_id'];


            $fields = ['password', 'confirm_password'];
            $formData = $this->getFormData($fields);
            $validationErrors = $this->val->password($formData);


            if (!empty($validationErrors)) {
                $data = [
                    'errors' => $validationErrors,
                    'old' => $formData,
                ];
                $this->redirect('/stores/edit?store='. $store, $data);
                $this->logError('Failed to validate');
                return;
            }
            $hashedPassword = password_hash($formData['password'], PASSWORD_DEFAULT);
            $data['password'] = $hashedPassword;

            if (!$this->model->updateElement($data, $id)) {
                $this->model->rollBack();
                return false;
            } else {
                $this->model->commit();
                $this->redirect('/stores/edit?store='. $store);
                return;
            }
        } catch (Exception $e) {
            $this->model->rollBack();
            $this->logError($e->getMessage());
            return $this->redirect('/error');
        }
    }

    public function update()
    {
        try {
            $this->model->beginTransaction();

            $id = $_GET['id'];

            $fields = ['name', 'last_name', 'phone', 'country', 'state', 'city', 'address'];
            $formData = $this->getFormData($fields);
            $validationErrors = $this->val->formData($formData);

            if (!empty($validationErrors)) {
                $data = [
                    'errors' => $validationErrors,
                    'old' => $formData,
                ];
                $this->redirect('/users/edit?user='. $id, $data);
                $this->logError('Failed to validate');
                return;
            }

            if (!$this->model->updateElement($formData, $id)) {
                $this->model->rollBack();
                return false;
            } else {
                $this->model->commit();
                $this->redirect('/users/edit?user='. $id);
                return;
            }
        } catch (Exception $e) {
            $this->model->rollBack();
            $this->logError($e->getMessage());
            return $this->redirect('/error');
        }
    }

    public function updatePassword(){
        try {
            $this->model->beginTransaction();

            $id = $_GET['id'];

            $fields = ['password', 'confirm_password'];
            $formData = $this->getFormData($fields);
            $validationErrors = $this->val->password($formData);


            if (!empty($validationErrors)) {
                $data = [
                    'errors' => $validationErrors,
                    'old' => $formData,
                ];
                $this->redirect('/stores/edit?store='. $store, $data);
                $this->logError('Failed to validate');
                return;
            }
            
            $hashedPassword = password_hash($formData['password'], PASSWORD_DEFAULT);
            $data['password'] = $hashedPassword;

            if (!$this->model->updateElement($data, $id)) {
                $this->model->rollBack();
                return false;
            } else {
                $this->model->commit();
                $this->redirect('/users/edit?user='. $id);
                return;
            }
        } catch (Exception $e) {
            $this->model->rollBack();
            $this->logError($e->getMessage());
            return $this->redirect('/error');
        }
    }

    private function validateFormData($data)
    {
        $errors = [];

        $requiredFields = ['name', 'email', 'username', 'password', 'confirm_password', 'phone'];
        foreach ($requiredFields as $field) {
            if (empty($data[$field])) {
                $errors[$field] = $this->formatFieldName($field) . ' is required!';
            }
        }

        if (!filter_var($data['email'], FILTER_VALIDATE_EMAIL)) {
            $errors['email'] = 'Invalid email address!';
        } elseif ($this->val->elementExists('email', $data['email'])) {
            $errors['email'] = 'Email is already registered.';
        }

        if ($this->val->elementExists('username', $data['username'])) {
            $errors['username'] = 'Username is already taken.';
        }

        if ($this->val->elementExists('phone', $data['phone'])) {
            $errors['username'] = 'Username is already taken.';
        }

        if ($data['password'] !== $data['confirm_password']) {
            $errors['confirm_password'] = 'Passwords do not match!';
        }

        // Password strength check
        if (strlen($data['password']) < 8) {
            $errors['password'] = 'Password must be at least 8 characters long!';
        }

        if (!preg_match('/[a-z]/', $data['password'])) {
            $errors['password'] = 'Password must contain at least one lowercase letter!';
        }

        if (!preg_match('/[A-Z]/', $data['password'])) {
            $errors['password'] = 'Password must contain at least one uppercase letter!';
        }

        if (!preg_match('/[0-9]/', $data['password'])) {
            $errors['password'] = 'Password must contain at least one number!';
        }

        if (!preg_match('/[!@#$%^&*()\-_=+{};:,<.>]/', $data['password'])) {
            $errors['password'] = 'Password must contain at least one special character!';
        }

        return $errors;
    }

    private function validateStoreFormData($data)
    {
        $errors = [];

        $requiredFields = ['store', 'name', 'last_name', 'email', 'username', 'password', 'confirm_password', 'phone'];
        foreach ($requiredFields as $field) {
            if (empty($data[$field])) {
                $errors[$field] = $this->formatFieldName($field) . ' is required!';
            }
        }

        if (!filter_var($data['email'], FILTER_VALIDATE_EMAIL)) {
            $errors['email'] = 'Invalid email address!';
        } elseif ($this->val->elementExists('email', $data['email'])) {
            $errors['email'] = 'Email is already registered.';
        }

        if ($this->val->elementExists('username', $data['username'])) {
            $errors['username'] = 'Username is already taken.';
        }

        if ($this->val->elementExists('phone', $data['phone'])) {
            $errors['username'] = 'Username is already taken.';
        }

        if ($data['password'] !== $data['confirm_password']) {
            $errors['confirm_password'] = 'Passwords do not match!';
        }

        // Password strength check
        if (strlen($data['password']) < 8) {
            $errors['password'] = 'Password must be at least 8 characters long!';
        }

        if (!preg_match('/[a-z]/', $data['password'])) {
            $errors['password'] = 'Password must contain at least one lowercase letter!';
        }

        if (!preg_match('/[A-Z]/', $data['password'])) {
            $errors['password'] = 'Password must contain at least one uppercase letter!';
        }

        if (!preg_match('/[0-9]/', $data['password'])) {
            $errors['password'] = 'Password must contain at least one number!';
        }

        if (!preg_match('/[!@#$%^&*()\-_=+{};:,<.>]/', $data['password'])) {
            $errors['password'] = 'Password must contain at least one special character!';
        }

        return $errors;
    }
}
